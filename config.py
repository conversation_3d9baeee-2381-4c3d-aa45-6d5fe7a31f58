"""
Configuration file for Florida Drug Overdose Chatbot
Centralized settings for easy customization and maintenance
"""

# Application Settings
APP_CONFIG = {
    "title": "Florida Drug Overdose Analytics",
    "version": "2.0",
    "description": "Advanced Query Interface for Medical Examiner Data",
    "page_icon": "💊",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Color Scheme
COLORS = {
    "primary": "#0021A5",      # Navy blue
    "secondary": "#FA4616",    # Orange red
    "accent": "#28A745",       # Green
    "warning": "#FFC107",      # Yellow
    "error": "#DC3545",        # Red
    "info": "#17A2B8",         # Cyan
    "light": "#F8F9FA",        # Light gray
    "dark": "#343A40"          # Dark gray
}

# Data Settings
DATA_CONFIG = {
    "csv_file": "mecDecedentTable (2).csv",
    "geojson_file": "geojson-fl-counties-fips.json",
    "geojson_url": "https://raw.githubusercontent.com/plotly/datasets/master/geojson-counties-fips.json",
    "florida_state_code": "12",
    "date_column": "date",
    "year_column": "year",
    "county_column": "county name",
    "age_column": "age",
    "sex_column": "sex",
    "race_column": "race"
}

# Query Processing Settings
QUERY_CONFIG = {
    "max_results_display": 100,
    "default_status": ["C", "P"],
    "timeout_seconds": 30,
    "max_query_length": 500,
    "enable_fuzzy_matching": True,
    "case_sensitive": False
}

# Visualization Settings
VIZ_CONFIG = {
    "default_chart_height": 400,
    "map_height": 600,
    "color_scales": {
        "sequential": "Reds",
        "diverging": "RdBu",
        "categorical": "Set3"
    },
    "chart_themes": {
        "plotly": "plotly_white",
        "seaborn": "whitegrid"
    }
}

# Drug Categories and Aliases
DRUG_CATEGORIES = {
    "opioids": {
        "substances": [
            "fentanyl", "heroin", "morphine", "oxycodone", "hydrocodone",
            "methadone", "buprenorphine", "oxymorphone", "hydromorphone",
            "tramadol", "u47700", "codeine"
        ],
        "aliases": {
            "fentanyl": ["fent", "fentanyl"],
            "heroin": ["heroin", "diacetylmorphine", "smack"],
            "oxycodone": ["oxy", "oxycodone", "oxycontin"],
            "hydrocodone": ["vicodin", "hydrocodone", "norco"],
            "morphine": ["morphine"],
            "methadone": ["methadone"],
            "buprenorphine": ["suboxone", "buprenorphine"],
            "tramadol": ["tramadol", "ultram"]
        }
    },
    "stimulants": {
        "substances": [
            "cocaine", "methamphetamine", "amphetamine", "mdma", "mda",
            "mdea", "cathinones", "phentermine"
        ],
        "aliases": {
            "cocaine": ["coke", "cocaine", "crack"],
            "methamphetamine": ["meth", "methamphetamine", "crystal", "ice"],
            "amphetamine": ["amphetamine", "speed"],
            "mdma": ["ecstasy", "molly", "mdma"],
            "phentermine": ["phentermine"]
        }
    },
    "depressants": {
        "substances": [
            "ethanol", "ghb", "carisoprodol"
        ],
        "aliases": {
            "ethanol": ["alcohol", "ethanol", "booze", "liquor"],
            "ghb": ["ghb", "liquid ecstasy"],
            "carisoprodol": ["soma", "carisoprodol"]
        }
    },
    "benzodiazepines": {
        "substances": [
            "alprazolam", "clonazepam", "diazepam", "lorazepam",
            "temazepam", "oxazepam", "midazolam", "triazolam",
            "flunitrazepam", "flurazepam", "estazolam", "chlordiazepoxide"
        ],
        "aliases": {
            "alprazolam": ["xanax", "alprazolam"],
            "clonazepam": ["klonopin", "clonazepam"],
            "diazepam": ["valium", "diazepam"],
            "lorazepam": ["ativan", "lorazepam"],
            "temazepam": ["restoril", "temazepam"],
            "midazolam": ["versed", "midazolam"]
        }
    },
    "hallucinogens": {
        "substances": [
            "pcp", "ketamine", "hallucinogenicphenethylamines",
            "hallucinogenictryptamines"
        ],
        "aliases": {
            "pcp": ["pcp", "angel dust"],
            "ketamine": ["ketamine", "special k"]
        }
    },
    "cannabis": {
        "substances": ["cannabinoids"],
        "aliases": {
            "cannabinoids": ["marijuana", "weed", "cannabis", "cannabinoids", "thc", "pot"]
        }
    },
    "inhalants": {
        "substances": [
            "freon", "toluene", "nitrousoxide", "helium",
            "halogenatedinhalants", "hydrocarboninhalants"
        ],
        "aliases": {
            "nitrousoxide": ["nitrous oxide", "laughing gas", "whippits"],
            "toluene": ["toluene", "paint thinner"],
            "freon": ["freon", "refrigerant"]
        }
    }
}

# Example Queries by Category
EXAMPLE_QUERIES = {
    "basic_counts": [
        "How many fentanyl deaths in 2022?",
        "Total cocaine cases in Miami-Dade",
        "Heroin deaths in Florida",
        "Count of oxycodone overdoses"
    ],
    "trends": [
        "Show fentanyl trends over time",
        "Chart cocaine deaths by year",
        "Plot heroin overdoses timeline",
        "Graph methamphetamine trends"
    ],
    "geographic": [
        "Map of fentanyl deaths in Florida",
        "Which county has most overdoses?",
        "Show Broward County cocaine deaths",
        "Compare deaths by county"
    ],
    "demographics": [
        "Demographics of fentanyl users",
        "Average age of heroin victims",
        "Male vs female cocaine deaths",
        "Show age distribution for opioids"
    ],
    "comparisons": [
        "Compare fentanyl vs heroin deaths",
        "Opioids vs stimulants in 2022",
        "Miami-Dade vs Broward overdoses",
        "2020 vs 2023 death counts"
    ],
    "advanced": [
        "Polydrug cases with fentanyl",
        "Suicide vs accident deaths",
        "Weekend vs weekday patterns",
        "Seasonal trends in overdoses"
    ]
}

# Intent Patterns for Query Parsing
INTENT_PATTERNS = {
    "count": [
        r"\b(how many|count|total|number of)\b",
        r"\b(cases|deaths|overdoses)\b"
    ],
    "map": [
        r"\b(map|geographic|spatial|location)\b",
        r"\b(by county|county map)\b"
    ],
    "plot_over_time": [
        r"\b(chart|plot|graph|trend|over time|timeline)\b",
        r"\b(by year|yearly|annual)\b"
    ],
    "table": [
        r"\b(table|data|list|show me|display)\b"
    ],
    "compare": [
        r"\b(compare|comparison|vs|versus|against)\b",
        r"\b(difference|contrast)\b"
    ],
    "demographics": [
        r"\b(demographics|age|gender|race|profile)\b",
        r"\b(male|female|men|women)\b"
    ],
    "most_by_county": [
        r"\b(most|highest|top|maximum)\b.*\b(county|counties)\b"
    ],
    "least_by_county": [
        r"\b(least|lowest|fewest|minimum)\b.*\b(county|counties)\b"
    ],
    "most_by_year": [
        r"\b(most|highest|peak)\b.*\b(year)\b"
    ],
    "first_appearance": [
        r"\b(first|earliest|initial|when did)\b"
    ],
    "average_age": [
        r"\b(average|mean|median)\b.*\b(age)\b"
    ],
    "polydrug_analysis": [
        r"\b(polydrug|multiple|combination)\b"
    ],
    "manner_of_death": [
        r"\b(suicide|homicide|accident|natural|manner)\b"
    ]
}

# Status Keywords
STATUS_KEYWORDS = {
    "cause": [
        "caused by", "cause of", "causes", "caused", "fatal", "lethal",
        "responsible for", "due to", "from"
    ],
    "present": [
        "present", "presence", "detected", "found", "with", "involving",
        "containing", "positive for", "had"
    ]
}

# Demographic Keywords
DEMOGRAPHIC_KEYWORDS = {
    "gender": {
        "male": ["male", "men", "man", "masculine"],
        "female": ["female", "women", "woman", "feminine"]
    },
    "age_groups": {
        "teens": (13, 19),
        "young_adults": (18, 35),
        "middle_aged": (35, 65),
        "elderly": (65, 999),
        "seniors": (65, 999)
    },
    "race": {
        "white": "White",
        "black": "Black",
        "hispanic": "Hispanic",
        "latino": "Hispanic",
        "asian": "Asian",
        "african_american": "Black"
    }
}

# Error Messages
ERROR_MESSAGES = {
    "file_not_found": "❌ Dataset file not found. Please ensure the file is in the correct directory.",
    "empty_query": "❌ Please enter a query to search the data.",
    "no_results": "📭 No records found matching your criteria. Try broadening your search.",
    "invalid_year": "❌ Invalid year specified. Please use years between 2003 and 2023.",
    "invalid_county": "❌ County not found. Please check the spelling or try a different county.",
    "drug_not_found": "❌ Substance not found in dataset. Try using a different name or check spelling.",
    "processing_error": "❌ An error occurred while processing your query. Please try again.",
    "timeout_error": "⏱️ Query timed out. Please try a simpler query or contact support."
}

# Success Messages
SUCCESS_MESSAGES = {
    "query_processed": "✅ Query processed successfully",
    "data_loaded": "✅ Data loaded successfully",
    "visualization_created": "✅ Visualization created",
    "export_complete": "✅ Export completed"
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "chatbot.log",
    "max_size": "10MB",
    "backup_count": 5
}

# Performance Settings
PERFORMANCE_CONFIG = {
    "cache_ttl": 3600,  # 1 hour
    "max_cache_size": "500MB",
    "enable_compression": True,
    "lazy_loading": True,
    "batch_size": 1000
}

# Feature Flags
FEATURE_FLAGS = {
    "enable_advanced_analytics": True,
    "enable_export_functionality": True,
    "enable_query_suggestions": True,
    "enable_performance_monitoring": True,
    "enable_user_feedback": True,
    "enable_query_history": True,
    "enable_data_validation": True,
    "enable_error_reporting": True
}
