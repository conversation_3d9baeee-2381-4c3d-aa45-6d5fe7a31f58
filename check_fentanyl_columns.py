"""
Quick check of fentanyl columns in the dataset
"""

import pandas as pd

try:
    # Load data
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower()
    
    print("🔍 Checking for fentanyl-related columns...")
    print(f"Total columns: {len(df.columns)}")
    
    # Find fentanyl columns
    fentanyl_cols = [col for col in df.columns if 'fentanyl' in col.lower()]
    print(f"\nFentanyl columns found: {len(fentanyl_cols)}")
    for col in fentanyl_cols:
        print(f"  - {col}")
    
    # Check a few sample values if fentanyl column exists
    if fentanyl_cols:
        main_fentanyl = fentanyl_cols[0]
        print(f"\nSample values in '{main_fentanyl}':")
        value_counts = df[main_fentanyl].value_counts()
        print(value_counts)
        
        # Check 2023 data
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df['year'] = df['date'].dt.year
        
        df_2023 = df[df['year'] == 2023]
        print(f"\nTotal records in 2023: {len(df_2023):,}")
        
        if len(df_2023) > 0:
            fentanyl_2023 = df_2023[df_2023[main_fentanyl].isin(['C', 'P'])]
            print(f"Fentanyl cases in 2023: {len(fentanyl_2023):,}")
        else:
            print("No 2023 data found")
    
    # Also check all drug-like columns
    print(f"\nAll columns containing common drug terms:")
    drug_terms = ['fentanyl', 'cocaine', 'heroin', 'morphine', 'oxycodone', 'alprazolam', 'ethanol']
    
    for term in drug_terms:
        matching_cols = [col for col in df.columns if term in col.lower()]
        if matching_cols:
            print(f"  {term}: {matching_cols}")

except Exception as e:
    print(f"Error: {e}")
