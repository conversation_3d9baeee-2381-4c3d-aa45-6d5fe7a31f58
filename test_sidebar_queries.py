"""
Simple test to validate sidebar queries work with intent detection
"""

import re

def test_sidebar_queries():
    """Test that all sidebar queries can be properly parsed"""
    
    # These are the exact queries from the sidebar
    sidebar_queries = [
        "How many fentanyl deaths in 2022?",
        "Show cocaine trends over time", 
        "What's the average age of heroin victims?",
        "How many cocaine deaths in 2023?",
        "Map of fentanyl deaths in Florida",
        "Which county has most overdoses?",
        "Show Miami-Dade cocaine deaths",
        "Map of heroin deaths in Florida",
        "Demographics of methamphetamine users",
        "Show data table for alprazolam",
        "Demographics of fentanyl users",
        "Show data table for cocaine",
        "Demographics of opioid users",
        "What's the average age of cocaine victims?",
        "Which county has most fentanyl deaths?",
        "Demographics of heroin users"
    ]
    
    # Intent patterns from the enhanced app (order matters - more specific patterns first)
    intent_patterns = [
        (r'\b(map)\b.*\b(florida|county|counties|deaths)\b', 'map'),
        (r'\b(map|geographic|spatial|location)\b', 'map'),
        (r'\b(show)\b.*\b(trend|over time)\b', 'plot_over_time'),
        (r'\b(chart|plot|graph|trend|over time|timeline)\b', 'plot_over_time'),
        (r'\b(show)\b.*\b(data table|table)\b', 'table'),
        (r'\b(table|data|list)\b.*\b(for|of)\b', 'table'),
        (r'\b(demographics)\b', 'demographics'),
        (r'\b(which county has most)\b', 'most_deaths_by_county'),
        (r'\b(most)\b.*\b(county|counties)\b', 'most_deaths_by_county'),
        (r'\b(least|lowest|fewest|minimum)\b.*\b(county|counties)\b', 'least_deaths_by_county'),
        (r'\b(most|highest|peak)\b.*\b(year)\b', 'most_deaths_by_year'),
        (r'\b(first|earliest|initial|when did)\b', 'first_appearance'),
        (r'\b(what\'s the average age|average age)\b', 'average_age'),
        (r'\b(average|mean|median)\b.*\b(age)\b', 'average_age'),
        (r'\b(polydrug|multiple|combination)\b', 'polydrug_analysis'),
        (r'\b(suicide|homicide|accident|natural|manner)\b', 'manner_of_death'),
        (r'\b(show)\b.*\b(miami-dade|broward|county)\b', 'count'),
        (r'\b(total|sum|count|how many)\b', 'count')
    ]
    
    print("🔍 Testing Sidebar Queries")
    print("=" * 50)
    
    all_working = True
    
    for i, query in enumerate(sidebar_queries, 1):
        text_lower = query.lower()
        intent_found = None
        
        # Find matching intent
        for pattern, intent in intent_patterns:
            if re.search(pattern, text_lower):
                intent_found = intent
                break
        
        if not intent_found:
            intent_found = "count"  # Default intent
        
        # Check if this intent is implemented
        implemented_intents = [
            'count', 'most_deaths_by_county', 'least_deaths_by_county', 
            'most_deaths_by_year', 'average_age', 'demographics', 
            'plot_over_time', 'map', 'table', 'first_appearance',
            'polydrug_analysis', 'manner_of_death'
        ]
        
        if intent_found in implemented_intents:
            status = "✅"
        else:
            status = "❌"
            all_working = False
        
        print(f"{i:2d}. {status} '{query}'")
        print(f"    → Intent: {intent_found}")
        print()
    
    print("=" * 50)
    if all_working:
        print("🎉 All sidebar queries should work!")
    else:
        print("⚠️ Some queries may not work properly.")
    
    return all_working

if __name__ == "__main__":
    test_sidebar_queries()
