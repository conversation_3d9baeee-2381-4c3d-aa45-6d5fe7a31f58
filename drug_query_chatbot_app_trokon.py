import streamlit as st
    # "Streamlit is an open-source Python framework for data scientists and AI/ML engineers
        # to deliver interactive data apps – in only a few lines of code."
import pandas as pd
import matplotlib.pyplot as plt
    # Usually a graph lib
import seaborn as sns
    # Usually a graph lib
import re
import plotly.express as px
    # Usually a graph lib
import os
import requests
    # "Requests allows you to send HTTP/1.1 requests extremely easily.""
        # Looks like it's used to get the geojson file
import json

# For all these graph libs, why use each one?


# Custom color palette
PRIMARY_COLOR = "#0021A5"
SECONDARY_COLOR = "#FA4616"

st.set_page_config(page_title="Florida Drug Overdose Chatbot", layout="wide")

@st.cache_data
def load_data():
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower()
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['year'] = df['date'].dt.year
    return df

df = load_data()

# Florida counties only GeoJSON
@st.cache_data
def load_geojson():
    filename = "geojson-fl-counties-fips.json"
    if not os.path.exists(filename):
        url = "https://raw.githubusercontent.com/plotly/datasets/master/geojson-counties-fips.json"
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "w") as f:
                f.write(response.text)
        else:
            st.error("Failed to download county GeoJSON file.")
            return None
    with open(filename) as f:
        geojson = json.load(f)
        # Filter to Florida only (STATEFP == '12')
        florida_features = [feat for feat in geojson["features"] if feat["properties"]["STATE"] == "12"]
        return {"type": "FeatureCollection", "features": florida_features}

counties_geojson = load_geojson()

non_drug_columns = ['id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
                    'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
                    'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
                    'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
                    'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
                    'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
                    'ethanol group', 'hallucinogenics group', 'inhalants group', 'year']

drug_columns = [col for col in df.columns if col not in non_drug_columns]

# Sidebar help
with st.sidebar:
    st.markdown(f"<h2 style='color:{PRIMARY_COLOR}'>🔎 Chatbot Help</h2>", unsafe_allow_html=True)
    st.markdown(
        f"<p style='color:{SECONDARY_COLOR}'>Example queries:</p>"
        "<ul>"
        "<li>How many fentanyl-related deaths in 2022?</li>"
        "<li>What county had the most cocaine deaths in 2021?</li>"
        "<li>Show a chart of heroin deaths by year</li>"
        "<li>Map of fentanyl deaths in 2023</li>"
        "</ul>",
        unsafe_allow_html=True,
    )

st.markdown(
    f"<h1 style='text-align:center; color:{PRIMARY_COLOR}'>💊 Florida Drug Overdose Query Chatbot</h1>",
    unsafe_allow_html=True,
)

user_input = st.text_input("Ask a question about overdose data:", "")

def parse_query(text):
    text = text.lower()
    query = {}

    year_match = re.search(r"(19|20)\d{2}", text)
    query["year"] = int(year_match.group()) if year_match else None

    found_drugs = [d for d in drug_columns if d in text]
    query["drug"] = found_drugs[0] if found_drugs else None

    query["cause"] = any(word in text for word in ["cause", "Cause", "c","C"])
    query["present"] = any(word in text for word in ["present", "p","Present","P"])
    if query["cause"] and query["present"]:
        query["status"] = ["C", "P"]
    elif query["cause"]:
        query["status"] = ["C"]
    elif query["present"]:
        query["status"] = ["P"]
    else:
        query["status"] = ["C", "P"]

    counties = df["county name"].dropna().unique()
    query["county"] = next((c for c in counties if c.lower() in text), None)

    if "map" in text:
        query["intent"] = "map"
    elif "most" in text and "county" in text:
        query["intent"] = "most_deaths_by_county"
    elif "least" in text or "fewest" in text:
        query["intent"] = "least_deaths_by_county"
    elif "most" in text and "year" in text:
        query["intent"] = "most_deaths_by_year"
    elif "chart" in text or "plot" in text or "graph" in text:
        query["intent"] = "plot_over_time"
    elif "first" in text or "earliest" in text:
        query["intent"] = "first_appearance"
    elif "table" in text:
        query["intent"] = "table"
    else:
        query["intent"] = "count"

    return query

def handle_query(query):
    dff = df.copy()

    if query["year"]:
        dff = dff[dff['year'] == query["year"]]

    if query["drug"] in dff.columns:
        dff = dff[dff[query["drug"]].isin(query["status"])]
    elif query["drug"] is not None:
        return f"🚫 Drug '{query['drug']}' not found in the dataset."

    if query["county"]:
        dff = dff[dff["county name"].str.lower() == query["county"].lower()]

    if query["intent"] == "count":
        return f"✅ {len(dff):,} matching cases found."

    elif query["intent"] == "most_deaths_by_county":
        result = dff["county name"].value_counts().head(1)
        if not result.empty:
            return f"🏆 {result.index[0]} with {result.iloc[0]:,} deaths"
        else:
            return "No results found."

    elif query["intent"] == "least_deaths_by_county":
        result = dff["county name"].value_counts().sort_values().head(1)
        if not result.empty:
            return f"📉 {result.index[0]} had the fewest deaths ({result.iloc[0]:,})"
        else:
            return "No results found."

    elif query["intent"] == "most_deaths_by_year":
        result = dff["year"].value_counts().head(1)
        if not result.empty:
            return f"📅 {result.index[0]} had the most cases ({result.iloc[0]:,})"
        else:
            return "No results found."

    elif query["intent"] == "first_appearance":
        first_year = df[df[query["drug"]].isin(query["status"])]["year"].min()
        return f"🕐 First appearance of {query['drug'].title()} ({' or '.join(query['status'])}) was in {first_year}."

    elif query["intent"] == "plot_over_time":
        st.subheader(f"📈 Trend Over Time for {query['drug'].title() if query['drug'] else 'All Drugs'}")
        if query["drug"]:
            trend_df = df[df[query["drug"]].isin(query["status"])].groupby("year").size()
        else:
            trend_df = df.groupby("year").size()
        fig, ax = plt.subplots()
        sns.set_theme(style="whitegrid")
        sns.lineplot(x=trend_df.index, y=trend_df.values, marker="o", color=PRIMARY_COLOR, ax=ax)
        ax.set_ylabel("Number of Deaths")
        ax.set_xlabel("Year")
        ax.set_title("Overdose Trend", color=SECONDARY_COLOR)
        st.pyplot(fig)
        return None

    elif query["intent"] == "map":
        if not counties_geojson:
            return "🌐 Could not load Florida counties GeoJSON."

        map_df = dff.groupby("county name").size().reset_index(name="count")
        map_df["county name"] = map_df["county name"].str.title()

        st.subheader("🗺️ Florida County Overdose Map")
        fig = px.choropleth(
            map_df,
            geojson=counties_geojson,
            locations="county name",
            featureidkey="properties.NAME",
            color="count",
            color_continuous_scale="Oranges",
            scope="usa",
            title=f"{query['drug'].title() if query['drug'] else 'All Drug'} Overdoses in Florida Counties"
        )
        fig.update_geos(fitbounds="locations", visible=False)
        st.plotly_chart(fig)
        return None

    else:
        return "⚠️ Sorry, I couldn't understand your question. Try rephrasing it."

if user_input:
    query = parse_query(user_input)
    response = handle_query(query)
    if response:
        st.success(response)
