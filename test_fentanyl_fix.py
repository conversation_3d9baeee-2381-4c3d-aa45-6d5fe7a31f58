"""
Test the fixed fentanyl query functionality
"""

import pandas as pd

def test_fentanyl_columns():
    """Test fentanyl column detection and data"""
    try:
        # Load data
        df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
        df.columns = df.columns.str.strip().str.lower()
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df['year'] = df['date'].dt.year
        
        print("🔍 Testing Fentanyl Query Fix")
        print("=" * 40)
        
        # Check available fentanyl columns
        fentanyl_cols = [col for col in df.columns if 'fentanyl' in col.lower()]
        print(f"All fentanyl columns: {fentanyl_cols}")
        
        # Focus on the two we want
        target_cols = ['fentanyl', 'fentanylanalogs']
        available_target_cols = [col for col in target_cols if col in df.columns]
        print(f"Target columns available: {available_target_cols}")
        
        # Test 2023 data
        df_2023 = df[df['year'] == 2023]
        print(f"\nTotal records in 2023: {len(df_2023):,}")
        
        total_fentanyl_2023 = 0
        
        for col in available_target_cols:
            print(f"\n📊 Checking '{col}' column in 2023:")
            
            # Check unique values
            values = df_2023[col].value_counts(dropna=False)
            print(f"  Unique values: {list(values.index[:5])}")  # Top 5 values
            
            # Count C and P values
            c_count = len(df_2023[df_2023[col] == 'C'])
            p_count = len(df_2023[df_2023[col] == 'P'])
            
            print(f"  'C' (cause) cases: {c_count:,}")
            print(f"  'P' (present) cases: {p_count:,}")
            print(f"  Total C+P: {c_count + p_count:,}")
            
            total_fentanyl_2023 += c_count + p_count
        
        print(f"\n🎯 TOTAL FENTANYL CASES IN 2023: {total_fentanyl_2023:,}")
        
        # Test the query simulation
        print(f"\n🧪 Simulating query: 'Number of fentanyl deaths in 2023'")
        
        # Simulate the drug matching logic
        query_text = "Number of fentanyl deaths in 2023"
        found_drugs = []
        
        # Check if fentanyl terms are in query
        if any(term in query_text.lower() for term in ['fentanyl', 'fent']):
            fentanyl_cols_available = [col for col in available_target_cols if col in df.columns]
            found_drugs.extend(fentanyl_cols_available)
        
        print(f"  Detected drugs: {found_drugs}")
        
        # Simulate filtering
        if found_drugs:
            conditions = []
            for drug in found_drugs:
                condition = df_2023[drug].isin(['C', 'P'])
                conditions.append(condition)
            
            if conditions:
                # Combine with OR
                combined = conditions[0]
                for cond in conditions[1:]:
                    combined = combined | cond
                
                filtered_data = df_2023[combined]
                result_count = len(filtered_data)
                
                print(f"  Final result: {result_count:,} cases")
                
                if result_count > 0:
                    print("  ✅ Query should work!")
                else:
                    print("  ⚠️ Query works but returns 0 results")
            else:
                print("  ❌ No valid conditions created")
        else:
            print("  ❌ No drugs detected")
        
        # Test a few other years for comparison
        print(f"\n📈 Fentanyl trends (recent years):")
        for year in [2020, 2021, 2022, 2023]:
            year_data = df[df['year'] == year]
            year_total = 0
            
            for col in available_target_cols:
                c_count = len(year_data[year_data[col] == 'C'])
                p_count = len(year_data[year_data[col] == 'P'])
                year_total += c_count + p_count
            
            print(f"  {year}: {year_total:,} cases")
        
        return total_fentanyl_2023 > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_fentanyl_columns()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Fentanyl query should work!")
    else:
        print("⚠️ Fentanyl query may have issues")
