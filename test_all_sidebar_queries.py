"""
Test all sidebar queries to ensure they work
"""

import re

def test_all_sidebar_queries():
    """Test that all sidebar queries will work with the current implementation"""
    
    # These are the exact queries from the sidebar
    sidebar_queries = [
        # Basic Counts
        "How many fentanyl deaths in 2022?",
        "How many cocaine deaths in 2023?", 
        "How many heroin deaths in 2021?",
        "How many alprazolam deaths in 2022?",
        
        # Geographic Maps
        "Map of fentanyl deaths in Florida",
        "Map of cocaine deaths in Florida",
        "Map of heroin deaths in Florida", 
        "Map of ethanol deaths in Florida",
        
        # County Analysis
        "Which county has most overdoses?",
        "Which county has most fentanyl deaths?",
        "Which county has most cocaine deaths?",
        "Which county has most heroin deaths?",
        
        # Trends Over Time
        "Show fentanyl trends over time",
        "Show cocaine trends over time",
        "Show heroin trends over time",
        "Show ethanol trends over time"
    ]
    
    # Test each query with the parse_query logic
    print("🧪 Testing All Sidebar Queries")
    print("=" * 50)
    
    all_working = True
    
    for i, query_text in enumerate(sidebar_queries, 1):
        print(f"\n{i:2d}. Testing: '{query_text}'")
        
        # Simulate the parse_query function
        text = query_text.lower()
        query = {}
        
        # Year extraction
        year_match = re.search(r"(19|20)\d{2}", text)
        query["year"] = int(year_match.group()) if year_match else None
        
        # Drug matching - simplified for test
        common_drugs = ['fentanyl', 'cocaine', 'heroin', 'alprazolam', 'ethanol', 'methamphetamine']
        found_drug = None
        for drug in common_drugs:
            if drug in text:
                found_drug = drug
                break
        
        # Check aliases
        if not found_drug:
            aliases = {'fent': 'fentanyl', 'coke': 'cocaine', 'alcohol': 'ethanol', 'xanax': 'alprazolam'}
            for alias, drug in aliases.items():
                if alias in text:
                    found_drug = drug
                    break
        
        query["drug"] = found_drug
        
        # Intent detection
        if "map" in text:
            query["intent"] = "map"
        elif "most" in text and "county" in text:
            query["intent"] = "most_deaths_by_county"
        elif "trend" in text or "over time" in text:
            query["intent"] = "plot_over_time"
        else:
            query["intent"] = "count"
        
        # Status
        query["status"] = ["C", "P"]  # Default
        
        # Validate the query
        issues = []
        
        if query["intent"] in ["count", "map", "plot_over_time"] and not query["drug"]:
            issues.append("No drug detected")
        
        if query["intent"] == "count" and not query["year"] and "deaths in" in text:
            issues.append("Year expected but not found")
        
        # Report results
        if issues:
            print(f"    ❌ Issues: {', '.join(issues)}")
            all_working = False
        else:
            print(f"    ✅ Should work")
            print(f"       Drug: {query['drug']}")
            print(f"       Year: {query['year']}")
            print(f"       Intent: {query['intent']}")
    
    print("\n" + "=" * 50)
    if all_working:
        print("🎉 ALL SIDEBAR QUERIES SHOULD WORK!")
    else:
        print("⚠️ Some queries may have issues")
    
    return all_working

if __name__ == "__main__":
    test_all_sidebar_queries()
