# Florida Drug Overdose Chatbot - Enhanced Version 2.0

## 🚀 Major Improvements & New Features

### 1. **Enhanced Query Processing**
- **Fuzzy Drug Matching**: Recognizes drug aliases (e.g., "xanax" → "alprazolam", "meth" → "methamphetamine")
- **Multi-Drug Queries**: Handle queries with multiple substances
- **Demographic Filtering**: Age ranges, gender, race filtering
- **Complex Intent Detection**: Comparisons, correlations, trends
- **Year Range Support**: "2020 to 2023", "last 5 years"

### 2. **Advanced Visualizations**
- **Interactive Time Series**: Multi-drug trend comparisons
- **Enhanced Maps**: Better color schemes and hover information
- **Demographic Breakdowns**: Age, gender, race distributions
- **Correlation Analysis**: Substance co-occurrence patterns
- **Temporal Patterns**: Monthly, seasonal, day-of-week analysis

### 3. **Robust Error Handling**
- **Comprehensive Logging**: Track errors and performance
- **Graceful Degradation**: App continues working even if some features fail
- **User-Friendly Messages**: Clear error explanations
- **Data Validation**: Input sanitization and validation
- **Timeout Protection**: Prevents hanging queries

### 4. **Improved User Experience**
- **Interactive Sidebar**: Clickable example queries
- **Query Interpretation**: Shows how your query was parsed
- **Quick Insights**: Dataset overview and key statistics
- **Progressive Disclosure**: Expandable sections for details
- **Performance Metrics**: Response time monitoring

### 5. **New Query Types**

#### **Basic Queries** (Enhanced)
```
"How many fentanyl deaths in Miami-Dade in 2022?"
"Total cocaine cases in Broward County"
"Count heroin overdoses in Florida"
```

#### **Demographic Analysis** (New)
```
"Demographics of fentanyl users"
"Average age of cocaine victims"
"Male vs female heroin deaths"
"Show elderly opioid deaths"
```

#### **Comparison Queries** (New)
```
"Compare fentanyl vs heroin deaths"
"Miami-Dade vs Broward overdoses"
"2020 vs 2023 death counts"
"Opioids vs stimulants trends"
```

#### **Advanced Analytics** (New)
```
"Polydrug cases with fentanyl"
"Seasonal trends in overdoses"
"Weekend vs weekday patterns"
"Correlation between substances"
```

#### **Geographic Analysis** (Enhanced)
```
"Map of cocaine deaths by county"
"Which counties have most fentanyl deaths?"
"Show geographic distribution of opioids"
"Compare northern vs southern Florida"
```

### 6. **Technical Improvements**

#### **Performance Optimizations**
- Efficient data filtering with pandas
- Cached computations for repeated queries
- Lazy loading of visualizations
- Optimized memory usage

#### **Code Quality**
- Type hints throughout
- Comprehensive error handling
- Modular architecture
- Configuration management
- Extensive logging

#### **Data Processing**
- Better column name handling
- Improved date parsing
- Enhanced county matching
- Robust drug categorization

## 📊 New Features in Detail

### **Enhanced Sidebar**
- **Dataset Overview**: Quick stats about the data
- **Example Queries**: Organized by category with clickable buttons
- **Available Substances**: Quick reference of tracked drugs
- **Query Tips**: Help for better query construction
- **Data Notes**: Important information about the dataset

### **Query Interpretation Display**
Shows users exactly how their query was parsed:
- Detected drugs and categories
- Time periods identified
- Geographic filters applied
- Intent classification
- Status settings (cause vs present)

### **Advanced Visualizations**
1. **Multi-Drug Trend Charts**: Compare multiple substances over time
2. **Demographic Breakdowns**: Comprehensive age, gender, race analysis
3. **Geographic Heat Maps**: Enhanced county-level visualizations
4. **Correlation Matrices**: Substance co-occurrence analysis
5. **Temporal Patterns**: Monthly, seasonal, and weekly patterns

### **Smart Drug Matching**
The system now recognizes:
- **Official Names**: "alprazolam", "methamphetamine"
- **Common Names**: "xanax", "meth", "crystal"
- **Street Names**: "molly", "ice", "crack"
- **Partial Matches**: "fent" → "fentanyl"

### **Demographic Filtering**
- **Age Ranges**: "teens", "elderly", "25 to 45"
- **Gender**: "male", "female", "men vs women"
- **Race/Ethnicity**: "white", "black", "hispanic"

## 🛠️ Installation & Setup

### **Requirements**
```bash
pip install streamlit pandas plotly matplotlib seaborn requests numpy
```

### **File Structure**
```
project/
├── drug_query_chatbot_app.py      # Main enhanced application
├── chatbot_enhancements.py        # Additional features
├── config.py                      # Configuration settings
├── mecDecedentTable (2).csv       # Dataset (required)
├── README_ENHANCEMENTS.md         # This file
└── logs/                          # Log files (auto-created)
```

### **Running the Application**
```bash
streamlit run drug_query_chatbot_app.py
```

## 🔧 Configuration

### **Customizing Colors**
Edit `config.py` to change the color scheme:
```python
COLORS = {
    "primary": "#0021A5",      # Navy blue
    "secondary": "#FA4616",    # Orange red
    "accent": "#28A745",       # Green
    # ... more colors
}
```

### **Adding New Drug Aliases**
Add to the `DRUG_ALIASES` dictionary in the main file:
```python
DRUG_ALIASES = {
    'fentanyl': ['fent', 'fentanyl', 'your_new_alias'],
    # ... more aliases
}
```

### **Modifying Example Queries**
Update the `query_categories` in the sidebar section:
```python
query_categories = {
    "Your Category": [
        "Your example query 1",
        "Your example query 2"
    ]
}
```

## 📈 Performance Monitoring

The enhanced version includes performance monitoring:
- Query response times
- Memory usage tracking
- Error rate monitoring
- Cache hit rates

Access via the sidebar "Performance Metrics" checkbox.

## 🐛 Troubleshooting

### **Common Issues**

1. **"Dataset file not found"**
   - Ensure `mecDecedentTable (2).csv` is in the same directory
   - Check file permissions

2. **"Geographic data unavailable"**
   - Internet connection required for first-time GeoJSON download
   - File will be cached locally after first download

3. **Slow performance**
   - Large queries may take time
   - Use more specific filters to improve speed
   - Check available memory

4. **Visualization not showing**
   - Check browser console for JavaScript errors
   - Try refreshing the page
   - Ensure Plotly is properly installed

### **Error Logging**
Errors are logged to `chatbot.log` with timestamps and details for debugging.

## 🔮 Future Enhancements

### **Planned Features**
- **Machine Learning Predictions**: Forecast future trends
- **Natural Language Generation**: Automated insights and summaries
- **Export Functionality**: PDF reports, CSV downloads
- **User Accounts**: Save queries and custom dashboards
- **Real-time Data**: Integration with live data feeds
- **Mobile Optimization**: Responsive design improvements

### **Advanced Analytics**
- **Statistical Testing**: Significance tests for comparisons
- **Clustering Analysis**: Identify patterns in overdose data
- **Anomaly Detection**: Automatically flag unusual patterns
- **Causal Analysis**: Explore relationships between variables

## 📞 Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Review the log files for error details
3. Ensure all dependencies are properly installed
4. Verify the dataset file is complete and properly formatted

## 📄 License & Data Usage

This tool is for research and educational purposes only. The underlying data comes from Florida Medical Examiners and should be used responsibly and in accordance with applicable data use policies.

---

**Version 2.0** - Enhanced Florida Drug Overdose Analytics Chatbot
*Advanced Query Interface for Medical Examiner Data*
