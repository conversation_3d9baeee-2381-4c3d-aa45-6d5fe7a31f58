"""
Test script for the enhanced Florida Drug Overdose Chatbot
Run this to validate that all enhancements are working properly
"""

import pandas as pd
import sys
import os
from datetime import datetime

def test_data_loading():
    """Test if the dataset loads properly"""
    print("🔍 Testing data loading...")
    
    try:
        df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
        df.columns = df.columns.str.strip().str.lower()
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df['year'] = df['date'].dt.year
        
        print(f"✅ Dataset loaded successfully: {len(df):,} records")
        print(f"   Columns: {len(df.columns)}")
        print(f"   Date range: {df['date'].min()} to {df['date'].max()}")
        return True, df
        
    except FileNotFoundError:
        print("❌ Dataset file 'mecDecedentTable (2).csv' not found")
        return False, None
    except Exception as e:
        print(f"❌ Error loading dataset: {str(e)}")
        return False, None

def test_drug_categories(df):
    """Test drug categorization"""
    print("\n🔍 Testing drug categorization...")
    
    try:
        NON_DRUG_COLUMNS = [
            'id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
            'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
            'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
            'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
            'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
            'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
            'ethanol group', 'hallucinogenics group', 'inhalants group', 'year'
        ]
        
        drug_columns = [col for col in df.columns if col not in NON_DRUG_COLUMNS]
        
        print(f"✅ Found {len(drug_columns)} drug columns")
        print(f"   Sample drugs: {', '.join(drug_columns[:5])}")
        
        # Test specific drugs
        key_drugs = ['fentanyl', 'cocaine', 'heroin', 'alprazolam', 'ethanol']
        found_drugs = [drug for drug in key_drugs if drug in drug_columns]
        
        print(f"   Key drugs found: {', '.join(found_drugs)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in drug categorization: {str(e)}")
        return False

def test_query_parsing():
    """Test the enhanced query parsing"""
    print("\n🔍 Testing query parsing...")

    # Test the actual sidebar queries to ensure they work
    sidebar_queries = [
        "How many fentanyl deaths in 2022?",
        "Show cocaine trends over time",
        "What's the average age of heroin victims?",
        "How many cocaine deaths in 2023?",
        "Map of fentanyl deaths in Florida",
        "Which county has most overdoses?",
        "Show Miami-Dade cocaine deaths",
        "Map of heroin deaths in Florida",
        "Demographics of methamphetamine users",
        "Show data table for alprazolam",
        "Demographics of fentanyl users",
        "Show data table for cocaine",
        "Demographics of opioid users",
        "What's the average age of cocaine victims?",
        "Which county has most fentanyl deaths?",
        "Demographics of heroin users"
    ]

    try:
        # Test intent detection patterns
        intent_patterns = [
            (r'\b(map|geographic|spatial|location)\b.*\b(florida|county|counties)\b', 'map'),
            (r'\b(map)\b', 'map'),
            (r'\b(chart|plot|graph|trend|over time|timeline)\b', 'plot_over_time'),
            (r'\b(show)\b.*\b(trend|chart|plot|graph)\b', 'plot_over_time'),
            (r'\b(demographics)\b.*\b(of|for)\b', 'demographics'),
            (r'\b(demographics|demographic)\b', 'demographics'),
            (r'\b(which county has most|most)\b.*\b(county|counties)\b', 'most_deaths_by_county'),
            (r'\b(average|mean|median)\b.*\b(age)\b', 'average_age'),
            (r'\b(what\'s the average age|average age)\b', 'average_age'),
            (r'\b(show)\b.*\b(data table|table)\b', 'table'),
            (r'\b(table|data|list)\b.*\b(for|of)\b', 'table'),
            (r'\b(show)\b.*\b(miami-dade|broward|county)\b', 'count'),
            (r'\b(total|sum|count|how many)\b', 'count')
        ]

        import re

        for query in sidebar_queries:
            text_lower = query.lower()
            intent_found = False

            for pattern, intent in intent_patterns:
                if re.search(pattern, text_lower):
                    print(f"   ✅ '{query}' → {intent}")
                    intent_found = True
                    break

            if not intent_found:
                print(f"   ⚠️ '{query}' → count (default)")

        print("✅ Query parsing tests completed")
        return True

    except Exception as e:
        print(f"❌ Error in query parsing: {str(e)}")
        return False

def test_data_filtering(df):
    """Test data filtering functionality"""
    print("\n🔍 Testing data filtering...")
    
    try:
        # Test year filtering
        year_2022 = df[df['year'] == 2022]
        print(f"   2022 data: {len(year_2022):,} records")
        
        # Test county filtering
        miami_dade = df[df['county name'].str.contains('Miami-Dade', case=False, na=False)]
        print(f"   Miami-Dade data: {len(miami_dade):,} records")
        
        # Test drug filtering (if fentanyl column exists)
        if 'fentanyl' in df.columns:
            fentanyl_cases = df[df['fentanyl'].isin(['C', 'P'])]
            print(f"   Fentanyl cases: {len(fentanyl_cases):,} records")
        
        print("✅ Data filtering tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error in data filtering: {str(e)}")
        return False

def test_visualization_data(df):
    """Test data preparation for visualizations"""
    print("\n🔍 Testing visualization data preparation...")
    
    try:
        # Test yearly aggregation
        yearly_counts = df.groupby('year').size()
        print(f"   Yearly data points: {len(yearly_counts)}")
        
        # Test county aggregation
        county_counts = df['county name'].value_counts()
        print(f"   Counties with data: {len(county_counts)}")
        
        # Test demographic data
        gender_dist = df['sex'].value_counts()
        print(f"   Gender categories: {len(gender_dist)}")
        
        age_stats = df['age'].describe()
        print(f"   Age range: {age_stats['min']:.0f} - {age_stats['max']:.0f}")
        
        print("✅ Visualization data preparation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error in visualization data preparation: {str(e)}")
        return False

def test_error_handling():
    """Test error handling capabilities"""
    print("\n🔍 Testing error handling...")
    
    try:
        # Test empty query handling
        empty_query = ""
        if not empty_query.strip():
            print("   ✅ Empty query detection works")
        
        # Test invalid year handling
        invalid_year = 1999  # Before dataset range
        if invalid_year < 2003 or invalid_year > 2023:
            print("   ✅ Invalid year detection works")
        
        # Test non-existent drug handling
        fake_drug = "nonexistentdrug123"
        print(f"   ✅ Non-existent drug '{fake_drug}' would be handled")
        
        print("✅ Error handling tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error in error handling tests: {str(e)}")
        return False

def test_performance():
    """Test basic performance metrics"""
    print("\n🔍 Testing performance...")
    
    try:
        start_time = datetime.now()
        
        # Simulate a data operation
        df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False, nrows=1000)
        
        end_time = datetime.now()
        load_time = (end_time - start_time).total_seconds()
        
        print(f"   Sample data load time: {load_time:.2f} seconds")
        
        if load_time < 5.0:
            print("✅ Performance test passed (< 5 seconds)")
            return True
        else:
            print("⚠️ Performance test warning (> 5 seconds)")
            return True
            
    except Exception as e:
        print(f"❌ Error in performance test: {str(e)}")
        return False

def run_all_tests():
    """Run all enhancement tests"""
    print("🚀 Running Enhanced Chatbot Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Data Loading
    success, df = test_data_loading()
    if success:
        tests_passed += 1
    
    if df is not None:
        # Test 2: Drug Categories
        if test_drug_categories(df):
            tests_passed += 1
        
        # Test 3: Data Filtering
        if test_data_filtering(df):
            tests_passed += 1
        
        # Test 4: Visualization Data
        if test_visualization_data(df):
            tests_passed += 1
    else:
        print("⚠️ Skipping tests that require data")
        total_tests = 3
    
    # Test 5: Query Parsing
    if test_query_parsing():
        tests_passed += 1
    
    # Test 6: Error Handling
    if test_error_handling():
        tests_passed += 1
    
    # Test 7: Performance
    if test_performance():
        tests_passed += 1
        total_tests += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your enhanced chatbot is ready to use.")
    elif tests_passed >= total_tests * 0.8:
        print("✅ Most tests passed. Minor issues may exist but app should work.")
    else:
        print("⚠️ Several tests failed. Please check your setup.")
    
    print("\n🚀 To run the enhanced chatbot:")
    print("   streamlit run drug_query_chatbot_app.py")

if __name__ == "__main__":
    run_all_tests()
