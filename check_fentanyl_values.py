"""
Check what values are actually in the fentanyl column
"""

import pandas as pd

try:
    # Load data
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower()
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['year'] = df['date'].dt.year
    
    print("🔍 Analyzing fentanyl column values...")
    
    # Check all unique values in fentanyl column
    print("\nAll unique values in 'fentanyl' column:")
    fentanyl_values = df['fentanyl'].value_counts(dropna=False)
    print(fentanyl_values)
    
    # Check 2023 specifically
    df_2023 = df[df['year'] == 2023]
    print(f"\n📅 2023 Data Analysis:")
    print(f"Total records in 2023: {len(df_2023):,}")
    
    if len(df_2023) > 0:
        print("\nFentanyl values in 2023:")
        fentanyl_2023_values = df_2023['fentanyl'].value_counts(dropna=False)
        print(fentanyl_2023_values)
        
        # Check for any non-null values
        non_null_fentanyl_2023 = df_2023[df_2023['fentanyl'].notna()]
        print(f"\nNon-null fentanyl entries in 2023: {len(non_null_fentanyl_2023)}")
        
        if len(non_null_fentanyl_2023) > 0:
            print("Sample non-null fentanyl entries in 2023:")
            print(non_null_fentanyl_2023[['fentanyl', 'date', 'county name']].head())
    
    # Check other years for comparison
    print(f"\n📊 Fentanyl cases by year (non-null values):")
    yearly_fentanyl = df[df['fentanyl'].notna()].groupby('year').size()
    print(yearly_fentanyl.tail(10))  # Last 10 years
    
    # Check if there are 'C' or 'P' values in any year
    cp_values = df[df['fentanyl'].isin(['C', 'P'])]
    print(f"\nRecords with fentanyl = 'C' or 'P': {len(cp_values)}")
    
    if len(cp_values) > 0:
        print("Years with C/P fentanyl values:")
        cp_years = cp_values.groupby('year').size()
        print(cp_years)
    
    # Check illicitfentanyl column for 2023
    print(f"\n💊 Checking 'illicitfentanyl' column for 2023:")
    illicit_2023 = df_2023['illicitfentanyl'].value_counts(dropna=False)
    print(illicit_2023)
    
    # Count "Yes" entries in illicitfentanyl for 2023
    illicit_yes_2023 = df_2023[df_2023['illicitfentanyl'].str.contains('Yes', case=False, na=False)]
    print(f"\nIllicit fentanyl 'Yes' cases in 2023: {len(illicit_yes_2023):,}")

except Exception as e:
    print(f"Error: {e}")
