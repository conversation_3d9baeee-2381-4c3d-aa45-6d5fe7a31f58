import pandas as pd
import numpy as np

def analyze_dataset():
    """Analyze the mecDecedentTable (2).csv dataset structure and contents"""
    
    # Load the dataset
    print("Loading dataset...")
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    
    print("="*80)
    print("FLORIDA DRUG OVERDOSE DATASET ANALYSIS")
    print("="*80)
    
    # Basic dataset info
    print(f"\n📊 DATASET OVERVIEW:")
    print(f"   • Total Records: {len(df):,}")
    print(f"   • Total Columns: {len(df.columns)}")
    print(f"   • File Size: ~{df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
    
    # Clean column names for analysis
    df.columns = df.columns.str.strip().str.lower()
    
    # Date range analysis
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['year'] = df['date'].dt.year
    
    print(f"\n📅 TIME PERIOD:")
    print(f"   • Date Range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"   • Years Covered: {df['year'].min():.0f} - {df['year'].max():.0f}")
    print(f"   • Total Years: {df['year'].nunique()} years")
    
    # Demographics
    print(f"\n👥 DEMOGRAPHICS:")
    print(f"   • Age Range: {df['age'].min():.0f} - {df['age'].max():.0f} years")
    print(f"   • Average Age: {df['age'].mean():.1f} years")
    print(f"   • Gender Distribution:")
    for gender, count in df['sex'].value_counts().items():
        print(f"     - {gender}: {count:,} ({count/len(df)*100:.1f}%)")
    
    print(f"   • Race Distribution:")
    for race, count in df['race'].value_counts().head(5).items():
        print(f"     - {race}: {count:,} ({count/len(df)*100:.1f}%)")
    
    # Geographic distribution
    print(f"\n🗺️ GEOGRAPHIC DISTRIBUTION:")
    print(f"   • Total Counties: {df['county name'].nunique()}")
    print(f"   • Top 5 Counties by Cases:")
    for county, count in df['county name'].value_counts().head(5).items():
        print(f"     - {county}: {count:,} cases")
    
    # Manner of death
    print(f"\n⚰️ MANNER OF DEATH:")
    for manner, count in df['manner of death'].value_counts().items():
        print(f"   • {manner}: {count:,} ({count/len(df)*100:.1f}%)")
    
    # Drug-related columns analysis
    non_drug_columns = ['id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
                        'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
                        'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
                        'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
                        'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
                        'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
                        'ethanol group', 'hallucinogenics group', 'inhalants group', 'year']
    
    drug_columns = [col for col in df.columns if col not in non_drug_columns]
    
    print(f"\n💊 DRUG ANALYSIS:")
    print(f"   • Total Drug Columns: {len(drug_columns)}")
    print(f"   • Drug Categories Available:")
    
    # Group drugs by category
    opioids = [col for col in drug_columns if any(x in col.lower() for x in ['fentanyl', 'heroin', 'morphine', 'oxycodone', 'hydrocodone', 'methadone', 'buprenorphine', 'oxymorphone', 'hydromorphone', 'tramadol', 'u47700'])]
    benzos = [col for col in drug_columns if any(x in col.lower() for x in ['alprazolam', 'clonazepam', 'diazepam', 'lorazepam', 'temazepam', 'oxazepam', 'midazolam', 'triazolam', 'flunitrazepam', 'flurazepam', 'estazolam', 'chlordiazepoxide'])]
    stimulants = [col for col in drug_columns if any(x in col.lower() for x in ['cocaine', 'methamphetamine', 'amphetamine', 'mdma', 'mda', 'mdea', 'cathinones', 'phentermine'])]
    
    print(f"     - Opioids: {len(opioids)} types")
    print(f"     - Benzodiazepines: {len(benzos)} types") 
    print(f"     - Stimulants: {len(stimulants)} types")
    print(f"     - Other substances: {len(drug_columns) - len(opioids) - len(benzos) - len(stimulants)} types")
    
    # Most common drugs
    print(f"\n🔝 TOP 10 MOST FREQUENTLY DETECTED DRUGS:")
    drug_counts = {}
    for drug in drug_columns:
        # Count both 'C' (cause) and 'P' (present) occurrences
        count = len(df[df[drug].isin(['C', 'P'])])
        if count > 0:
            drug_counts[drug] = count
    
    top_drugs = sorted(drug_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    for i, (drug, count) in enumerate(top_drugs, 1):
        print(f"   {i:2d}. {drug.title()}: {count:,} cases ({count/len(df)*100:.1f}%)")
    
    # Polydrug use
    print(f"\n🔄 POLYDRUG USE:")
    poly_cases = df[df['poly'] == 1]
    print(f"   • Cases with multiple substances: {len(poly_cases):,} ({len(poly_cases)/len(df)*100:.1f}%)")
    print(f"   • Average substances per case: {df['totalsubstancecount'].mean():.1f}")
    print(f"   • Max substances in single case: {df['totalsubstancecount'].max():.0f}")
    
    # Yearly trends
    print(f"\n📈 YEARLY TRENDS:")
    yearly_counts = df.groupby('year').size().sort_index()
    print(f"   • Cases by Year:")
    for year, count in yearly_counts.items():
        if pd.notna(year):
            print(f"     - {year:.0f}: {count:,} cases")
    
    # Data quality
    print(f"\n🔍 DATA QUALITY:")
    print(f"   • Missing Values by Key Columns:")
    key_columns = ['age', 'sex', 'race', 'county name', 'date', 'cause of death']
    for col in key_columns:
        missing = df[col].isna().sum()
        print(f"     - {col}: {missing:,} missing ({missing/len(df)*100:.1f}%)")
    
    print(f"\n📋 COLUMN STRUCTURE:")
    print(f"   • Administrative Columns: {len([col for col in df.columns if col in ['id', 'decedentkey', 'uf case number']])}")
    print(f"   • Demographic Columns: {len([col for col in df.columns if col in ['age', 'sex', 'race']])}")
    print(f"   • Geographic Columns: {len([col for col in df.columns if col in ['county', 'county name', 'county number', 'district']])}")
    print(f"   • Death Classification: {len([col for col in df.columns if col in ['manner of death', 'cause of death']])}")
    print(f"   • Substance Detection: {len(drug_columns)} individual drugs")
    print(f"   • Summary Statistics: {len([col for col in df.columns if 'count' in col or 'group' in col])}")
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)

if __name__ == "__main__":
    analyze_dataset()
