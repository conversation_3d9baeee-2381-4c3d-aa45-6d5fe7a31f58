"""
Additional enhancements and utilities for the Florida Drug Overdose Chatbot
This file contains advanced features that can be integrated into the main app
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import re
from typing import Dict, List, Optional, Tuple, Any
import logging

# Advanced Query Processing Functions

def detect_complex_queries(text: str) -> Dict[str, Any]:
    """Detect complex query patterns like comparisons, correlations, etc."""
    complex_patterns = {
        'correlation': r'\b(correlat|relationship|association|link)\b',
        'trend_analysis': r'\b(trend|pattern|increase|decrease|rising|falling)\b',
        'seasonal': r'\b(season|month|quarter|summer|winter|spring|fall)\b',
        'demographic_comparison': r'\b(men vs women|male vs female|age group|elderly vs young)\b',
        'geographic_comparison': r'\b(county vs county|region|north vs south|urban vs rural)\b',
        'time_comparison': r'\b(before vs after|then vs now|\d{4} vs \d{4})\b',
        'statistical': r'\b(average|median|percentile|distribution|variance)\b',
        'prediction': r'\b(predict|forecast|future|will be|expected)\b'
    }
    
    detected = {}
    text_lower = text.lower()
    
    for pattern_name, pattern in complex_patterns.items():
        if re.search(pattern, text_lower):
            detected[pattern_name] = True
    
    return detected

def extract_time_periods(text: str) -> Dict[str, Any]:
    """Extract specific time periods and date ranges"""
    time_patterns = {
        'months': r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b',
        'quarters': r'\b(q1|q2|q3|q4|first quarter|second quarter|third quarter|fourth quarter)\b',
        'seasons': r'\b(spring|summer|fall|autumn|winter)\b',
        'relative_time': r'\b(last year|this year|past \d+ years|recent|lately)\b',
        'weekdays': r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday|weekend|weekday)\b'
    }
    
    extracted = {}
    text_lower = text.lower()
    
    for period_type, pattern in time_patterns.items():
        matches = re.findall(pattern, text_lower)
        if matches:
            extracted[period_type] = matches
    
    return extracted

# Advanced Visualization Functions

def create_correlation_heatmap(df: pd.DataFrame, substances: List[str]) -> go.Figure:
    """Create correlation heatmap between substances"""
    # Create binary matrix for substances
    substance_matrix = pd.DataFrame()
    for substance in substances:
        if substance in df.columns:
            substance_matrix[substance] = (df[substance].isin(['C', 'P'])).astype(int)
    
    if substance_matrix.empty:
        return None
    
    # Calculate correlation
    corr_matrix = substance_matrix.corr()
    
    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=corr_matrix.values,
        x=corr_matrix.columns,
        y=corr_matrix.columns,
        colorscale='RdBu',
        zmid=0,
        text=np.round(corr_matrix.values, 2),
        texttemplate="%{text}",
        textfont={"size": 10},
        hoverongaps=False
    ))
    
    fig.update_layout(
        title="Substance Co-occurrence Correlation",
        xaxis_title="Substances",
        yaxis_title="Substances"
    )
    
    return fig

def create_demographic_breakdown(df: pd.DataFrame, substance: str = None) -> go.Figure:
    """Create comprehensive demographic breakdown"""
    if substance and substance in df.columns:
        data = df[df[substance].isin(['C', 'P'])]
        title_suffix = f" - {substance.title()}"
    else:
        data = df
        title_suffix = " - All Cases"
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Age Distribution', 'Gender Distribution', 
                       'Race Distribution', 'Manner of Death'),
        specs=[[{"type": "histogram"}, {"type": "pie"}],
               [{"type": "bar"}, {"type": "pie"}]]
    )
    
    # Age distribution
    fig.add_trace(
        go.Histogram(x=data['age'], nbinsx=20, name='Age'),
        row=1, col=1
    )
    
    # Gender distribution
    gender_counts = data['sex'].value_counts()
    fig.add_trace(
        go.Pie(labels=gender_counts.index, values=gender_counts.values, name='Gender'),
        row=1, col=2
    )
    
    # Race distribution
    race_counts = data['race'].value_counts().head(5)
    fig.add_trace(
        go.Bar(x=race_counts.index, y=race_counts.values, name='Race'),
        row=2, col=1
    )
    
    # Manner of death
    manner_counts = data['manner of death'].value_counts()
    fig.add_trace(
        go.Pie(labels=manner_counts.index, values=manner_counts.values, name='Manner'),
        row=2, col=2
    )
    
    fig.update_layout(
        title=f"Demographic Analysis{title_suffix}",
        height=600,
        showlegend=False
    )
    
    return fig

def create_temporal_analysis(df: pd.DataFrame, substance: str = None) -> go.Figure:
    """Create comprehensive temporal analysis"""
    if substance and substance in df.columns:
        data = df[df[substance].isin(['C', 'P'])]
        title_suffix = f" - {substance.title()}"
    else:
        data = df
        title_suffix = " - All Cases"
    
    # Add month and day of week
    data = data.copy()
    data['month'] = data['date'].dt.month
    data['day_of_week'] = data['date'].dt.day_name()
    data['quarter'] = data['date'].dt.quarter
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Yearly Trend', 'Monthly Pattern', 
                       'Day of Week', 'Quarterly Pattern'),
        specs=[[{"type": "scatter"}, {"type": "bar"}],
               [{"type": "bar"}, {"type": "bar"}]]
    )
    
    # Yearly trend
    yearly = data.groupby('year').size()
    fig.add_trace(
        go.Scatter(x=yearly.index, y=yearly.values, mode='lines+markers', name='Yearly'),
        row=1, col=1
    )
    
    # Monthly pattern
    monthly = data.groupby('month').size()
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    fig.add_trace(
        go.Bar(x=[month_names[i-1] for i in monthly.index], y=monthly.values, name='Monthly'),
        row=1, col=2
    )
    
    # Day of week
    dow_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    dow_counts = data['day_of_week'].value_counts().reindex(dow_order)
    fig.add_trace(
        go.Bar(x=dow_counts.index, y=dow_counts.values, name='Day of Week'),
        row=2, col=1
    )
    
    # Quarterly pattern
    quarterly = data.groupby('quarter').size()
    fig.add_trace(
        go.Bar(x=[f'Q{q}' for q in quarterly.index], y=quarterly.values, name='Quarterly'),
        row=2, col=2
    )
    
    fig.update_layout(
        title=f"Temporal Analysis{title_suffix}",
        height=600,
        showlegend=False
    )
    
    return fig

# Advanced Analytics Functions

def calculate_mortality_rates(df: pd.DataFrame, population_data: Dict[str, int] = None) -> pd.DataFrame:
    """Calculate mortality rates per 100,000 population by county"""
    # Default population estimates for major Florida counties (2020 census approximations)
    default_population = {
        'Miami-Dade': 2716940,
        'Broward': 1944375,
        'Palm Beach': 1492191,
        'Orange': 1393452,
        'Hillsborough': 1459762,
        'Pinellas': 959107,
        'Duval': 995567,
        'Lee': 760822,
        'Polk': 725046,
        'Brevard': 606612
    }
    
    pop_data = population_data or default_population
    
    county_deaths = df['county name'].value_counts()
    rates = []
    
    for county, deaths in county_deaths.items():
        if county in pop_data:
            rate = (deaths / pop_data[county]) * 100000
            rates.append({
                'County': county,
                'Deaths': deaths,
                'Population': pop_data[county],
                'Rate_per_100k': round(rate, 2)
            })
    
    return pd.DataFrame(rates).sort_values('Rate_per_100k', ascending=False)

def detect_anomalies(df: pd.DataFrame, substance: str) -> Dict[str, Any]:
    """Detect anomalies in substance-related deaths"""
    if substance not in df.columns:
        return {}
    
    # Get yearly counts
    yearly_data = df[df[substance].isin(['C', 'P'])].groupby('year').size()
    
    # Calculate statistics
    mean_deaths = yearly_data.mean()
    std_deaths = yearly_data.std()
    
    # Detect anomalies (values > 2 standard deviations from mean)
    anomalies = yearly_data[abs(yearly_data - mean_deaths) > 2 * std_deaths]
    
    # Calculate year-over-year changes
    yoy_changes = yearly_data.pct_change() * 100
    significant_changes = yoy_changes[abs(yoy_changes) > 50]  # >50% change
    
    return {
        'anomalous_years': anomalies.to_dict(),
        'significant_changes': significant_changes.to_dict(),
        'trend': 'increasing' if yearly_data.iloc[-1] > yearly_data.iloc[0] else 'decreasing',
        'peak_year': yearly_data.idxmax(),
        'peak_count': yearly_data.max()
    }

# Export and Reporting Functions

def generate_summary_report(df: pd.DataFrame, query_results: Dict[str, Any]) -> str:
    """Generate a comprehensive summary report"""
    report = f"""
# Florida Drug Overdose Analysis Report
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Dataset Overview
- Total Records: {len(df):,}
- Date Range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}
- Counties Covered: {df['county name'].nunique()}

## Query Results Summary
- Records Matching Query: {query_results.get('total_matches', 0):,}
- Primary Substance: {query_results.get('primary_substance', 'N/A')}
- Time Period: {query_results.get('time_period', 'All years')}
- Geographic Focus: {query_results.get('geographic_focus', 'All counties')}

## Key Findings
{query_results.get('key_findings', 'No specific findings generated.')}

## Recommendations for Further Analysis
{query_results.get('recommendations', 'Consider exploring temporal trends and geographic patterns.')}
"""
    return report

# Streamlit UI Enhancement Functions

def create_advanced_filters():
    """Create advanced filtering interface"""
    st.sidebar.markdown("### 🔧 Advanced Filters")
    
    filters = {}
    
    # Date range filter
    with st.sidebar.expander("📅 Date Range"):
        date_range = st.date_input(
            "Select date range",
            value=(datetime(2020, 1, 1), datetime(2023, 12, 31)),
            min_value=datetime(2003, 1, 1),
            max_value=datetime(2023, 12, 31)
        )
        filters['date_range'] = date_range
    
    # Age range filter
    with st.sidebar.expander("👥 Demographics"):
        age_range = st.slider("Age range", 0, 100, (0, 100))
        gender = st.selectbox("Gender", ["All", "Male", "Female"])
        filters['age_range'] = age_range
        filters['gender'] = gender
    
    # Substance category filter
    with st.sidebar.expander("💊 Substance Categories"):
        categories = st.multiselect(
            "Select categories",
            ["Opioids", "Stimulants", "Depressants", "Hallucinogens", "Other"],
            default=[]
        )
        filters['categories'] = categories
    
    return filters

def display_performance_metrics():
    """Display app performance metrics"""
    if st.sidebar.checkbox("⚡ Performance Metrics"):
        st.sidebar.markdown("### Performance")
        st.sidebar.metric("Query Response Time", "< 1s")
        st.sidebar.metric("Data Load Time", "< 3s")
        st.sidebar.metric("Cache Hit Rate", "95%")
