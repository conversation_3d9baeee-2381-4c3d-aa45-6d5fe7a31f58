"""
Test script to verify the "Number of fentanyl deaths in 2023" query works
"""

import pandas as pd
import re
from typing import Dict, List, Optional, Tuple, Any

def load_test_data():
    """Load the dataset for testing"""
    try:
        df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
        df.columns = df.columns.str.strip().str.lower()
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df['year'] = df['date'].dt.year
        return df
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def test_drug_columns(df):
    """Test what drug columns are available"""
    NON_DRUG_COLUMNS = [
        'id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
        'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
        'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
        'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
        'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
        'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
        'ethanol group', 'hallucinogenics group', 'inhalants group', 'year'
    ]
    
    drug_columns = [col for col in df.columns if col not in NON_DRUG_COLUMNS]
    
    print("🔍 Available drug columns:")
    for i, drug in enumerate(drug_columns, 1):
        print(f"  {i:2d}. {drug}")
    
    # Check for fentanyl specifically
    fentanyl_cols = [col for col in drug_columns if 'fentanyl' in col.lower()]
    print(f"\n💊 Fentanyl-related columns: {fentanyl_cols}")
    
    return drug_columns

def test_fentanyl_matching(drug_columns):
    """Test fentanyl drug matching"""
    text = "Number of fentanyl deaths in 2023"
    text_lower = text.lower()
    
    print(f"\n🔍 Testing query: '{text}'")
    print(f"   Lowercase: '{text_lower}'")
    
    # Test direct matching
    found_drugs = []
    for drug in drug_columns:
        if drug.lower() in text_lower:
            found_drugs.append(drug)
            print(f"   ✅ Direct match found: {drug}")
    
    # Test alias matching
    DRUG_ALIASES = {
        'fentanyl': ['fent', 'fentanyl'],
        'cocaine': ['coke', 'cocaine'],
        'heroin': ['heroin', 'diacetylmorphine'],
    }
    
    for drug, aliases in DRUG_ALIASES.items():
        for alias in aliases:
            if alias.lower() in text_lower:
                matching_cols = [col for col in drug_columns if drug.lower() in col.lower()]
                found_drugs.extend(matching_cols)
                print(f"   ✅ Alias match '{alias}' → {matching_cols}")
    
    print(f"   Final found drugs: {list(set(found_drugs))}")
    return list(set(found_drugs))

def test_intent_detection():
    """Test intent detection for the query"""
    text = "Number of fentanyl deaths in 2023"
    text_lower = text.lower()
    
    intent_patterns = [
        (r'\b(map)\b.*\b(florida|county|counties|deaths)\b', 'map'),
        (r'\b(map|geographic|spatial|location)\b', 'map'),
        (r'\b(show)\b.*\b(trend|over time)\b', 'plot_over_time'),
        (r'\b(chart|plot|graph|trend|over time|timeline)\b', 'plot_over_time'),
        (r'\b(show)\b.*\b(data table|table)\b', 'table'),
        (r'\b(table|data|list)\b.*\b(for|of)\b', 'table'),
        (r'\b(demographics)\b', 'demographics'),
        (r'\b(which county has most)\b', 'most_deaths_by_county'),
        (r'\b(most)\b.*\b(county|counties)\b', 'most_deaths_by_county'),
        (r'\b(least|lowest|fewest|minimum)\b.*\b(county|counties)\b', 'least_deaths_by_county'),
        (r'\b(most|highest|peak)\b.*\b(year)\b', 'most_deaths_by_year'),
        (r'\b(first|earliest|initial|when did)\b', 'first_appearance'),
        (r'\b(what\'s the average age|average age)\b', 'average_age'),
        (r'\b(average|mean|median)\b.*\b(age)\b', 'average_age'),
        (r'\b(polydrug|multiple|combination)\b', 'polydrug_analysis'),
        (r'\b(suicide|homicide|accident|natural|manner)\b', 'manner_of_death'),
        (r'\b(show)\b.*\b(miami-dade|broward|county)\b', 'count'),
        (r'\b(number of|total|sum|count|how many)\b', 'count')
    ]
    
    print(f"\n🎯 Testing intent detection for: '{text}'")
    
    for pattern, intent in intent_patterns:
        if re.search(pattern, text_lower):
            print(f"   ✅ Matched pattern '{pattern}' → Intent: {intent}")
            return intent
    
    print("   ⚠️ No pattern matched, defaulting to 'count'")
    return 'count'

def test_year_extraction():
    """Test year extraction"""
    text = "Number of fentanyl deaths in 2023"
    
    year_matches = re.findall(r'(19|20)\d{2}', text)
    if year_matches:
        years = [int(year) for year in year_matches]
        print(f"\n📅 Years extracted: {years}")
        return years
    else:
        print("\n📅 No years found")
        return []

def test_full_query(df, drug_columns):
    """Test the complete query processing"""
    text = "Number of fentanyl deaths in 2023"
    
    print(f"\n🔄 Testing full query: '{text}'")
    
    # Extract components
    found_drugs = test_fentanyl_matching(drug_columns)
    intent = test_intent_detection()
    years = test_year_extraction()
    
    if not found_drugs:
        print("   ❌ No drugs found - query will fail")
        return
    
    if not years:
        print("   ❌ No years found - query will fail")
        return
    
    # Test data filtering
    drug = found_drugs[0]
    year = years[0]
    
    print(f"\n📊 Filtering data for {drug} in {year}...")
    
    # Filter by year
    year_data = df[df['year'] == year]
    print(f"   Records in {year}: {len(year_data):,}")
    
    # Filter by drug
    if drug in df.columns:
        drug_data = year_data[year_data[drug].isin(['C', 'P'])]
        print(f"   {drug} cases in {year}: {len(drug_data):,}")
        
        if len(drug_data) > 0:
            print("   ✅ Query should work!")
            return len(drug_data)
        else:
            print("   ⚠️ No matching records found")
            return 0
    else:
        print(f"   ❌ Column '{drug}' not found in dataset")
        return None

def main():
    print("🧪 Testing 'Number of fentanyl deaths in 2023' Query")
    print("=" * 60)
    
    # Load data
    df = load_test_data()
    if df is None:
        print("❌ Cannot load data - test failed")
        return
    
    print(f"✅ Data loaded: {len(df):,} records")
    
    # Test drug columns
    drug_columns = test_drug_columns(df)
    
    # Test the full query
    result = test_full_query(df, drug_columns)
    
    print("\n" + "=" * 60)
    if result is not None and result > 0:
        print(f"🎉 SUCCESS: Query should return {result:,} fentanyl deaths in 2023")
    elif result == 0:
        print("⚠️ WARNING: Query works but returns 0 results (no fentanyl deaths in 2023)")
    else:
        print("❌ FAILURE: Query will not work properly")

if __name__ == "__main__":
    main()
