"""
Simple check of the 'fentanyl' column only
"""

import pandas as pd

try:
    # Load data
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower()
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['year'] = df['date'].dt.year
    
    print("🔍 Checking 'fentanyl' column only...")
    
    # Check unique values in fentanyl column
    print("\nUnique values in 'fentanyl' column:")
    fentanyl_values = df['fentanyl'].value_counts(dropna=False)
    print(fentanyl_values.head(10))
    
    # Check 2023 data
    df_2023 = df[df['year'] == 2023]
    print(f"\nTotal records in 2023: {len(df_2023):,}")
    
    print("\nFentanyl column values in 2023:")
    fentanyl_2023 = df_2023['fentanyl'].value_counts(dropna=False)
    print(fentanyl_2023.head(10))
    
    # Check for C and P values specifically
    c_values_2023 = len(df_2023[df_2023['fentanyl'] == 'C'])
    p_values_2023 = len(df_2023[df_2023['fentanyl'] == 'P'])
    
    print(f"\nFentanyl 'C' (cause) cases in 2023: {c_values_2023}")
    print(f"Fentanyl 'P' (present) cases in 2023: {p_values_2023}")
    print(f"Total fentanyl C+P cases in 2023: {c_values_2023 + p_values_2023}")
    
    # Check a few other recent years
    for year in [2021, 2022, 2023]:
        year_data = df[df['year'] == year]
        c_count = len(year_data[year_data['fentanyl'] == 'C'])
        p_count = len(year_data[year_data['fentanyl'] == 'P'])
        print(f"{year}: C={c_count}, P={p_count}, Total={c_count+p_count}")

except Exception as e:
    print(f"Error: {e}")
