import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import re
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import requests
import json
import logging
from datetime import datetime
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Custom color palette
PRIMARY_COLOR = "#0021A5"
SECONDARY_COLOR = "#FA4616"
ACCENT_COLOR = "#28A745"
WARNING_COLOR = "#FFC107"
ERROR_COLOR = "#DC3545"

# Page configuration
st.set_page_config(
    page_title="Florida Drug Overdose Analytics Chatbot",
    layout="wide",
    initial_sidebar_state="expanded",
    page_icon="💊"
)

@st.cache_data
def load_data():
    """Load and preprocess the dataset with error handling"""
    try:
        logger.info("Loading dataset...")
        df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)

        # Clean column names
        df.columns = df.columns.str.strip().str.lower()

        # Process dates
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df['year'] = df['date'].dt.year

        # Data validation
        if df.empty:
            raise ValueError("Dataset is empty")

        logger.info(f"Dataset loaded successfully: {len(df):,} records, {len(df.columns)} columns")
        return df

    except FileNotFoundError:
        st.error("❌ Dataset file 'mecDecedentTable (2).csv' not found. Please ensure the file is in the correct directory.")
        st.stop()
    except Exception as e:
        st.error(f"❌ Error loading dataset: {str(e)}")
        logger.error(f"Error loading dataset: {str(e)}")
        st.stop()

# Load data with error handling
try:
    df = load_data()
except Exception as e:
    st.error("Failed to load data. Please check the dataset file.")
    st.stop()

@st.cache_data
def load_geojson():
    """Load Florida counties GeoJSON with enhanced error handling"""
    filename = "geojson-fl-counties-fips.json"

    try:
        if not os.path.exists(filename):
            logger.info("Downloading Florida counties GeoJSON...")
            url = "https://raw.githubusercontent.com/plotly/datasets/master/geojson-counties-fips.json"

            with st.spinner("Downloading geographic data..."):
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                with open(filename, "w") as f:
                    f.write(response.text)
                logger.info("GeoJSON file downloaded successfully")

        with open(filename) as f:
            geojson = json.load(f)

        # Filter to Florida only (STATE == "12")
        florida_features = [
            feat for feat in geojson["features"]
            if feat["properties"].get("STATE") == "12"
        ]

        if not florida_features:
            logger.warning("No Florida counties found in GeoJSON")
            return None

        logger.info(f"Loaded {len(florida_features)} Florida counties")
        return {"type": "FeatureCollection", "features": florida_features}

    except requests.RequestException as e:
        logger.error(f"Failed to download GeoJSON: {str(e)}")
        st.warning("⚠️ Could not download geographic data. Maps will be unavailable.")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in GeoJSON file: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error loading GeoJSON: {str(e)}")
        return None

counties_geojson = load_geojson()

# Enhanced data categorization
NON_DRUG_COLUMNS = [
    'id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
    'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
    'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
    'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
    'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
    'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
    'ethanol group', 'hallucinogenics group', 'inhalants group', 'year'
]

# Get drug columns
drug_columns = [col for col in df.columns if col not in NON_DRUG_COLUMNS]

# Create drug categories for better organization
DRUG_CATEGORIES = {
    'opioids': [col for col in drug_columns if any(x in col.lower() for x in
                ['fentanyl', 'heroin', 'morphine', 'oxycodone', 'hydrocodone',
                 'methadone', 'buprenorphine', 'oxymorphone', 'hydromorphone',
                 'tramadol', 'u47700', 'codeine'])],
    'benzodiazepines': [col for col in drug_columns if any(x in col.lower() for x in
                       ['alprazolam', 'clonazepam', 'diazepam', 'lorazepam',
                        'temazepam', 'oxazepam', 'midazolam', 'triazolam',
                        'flunitrazepam', 'flurazepam', 'estazolam', 'chlordiazepoxide'])],
    'stimulants': [col for col in drug_columns if any(x in col.lower() for x in
                  ['cocaine', 'methamphetamine', 'amphetamine', 'mdma', 'mda',
                   'mdea', 'cathinones', 'phentermine'])],
    'depressants': [col for col in drug_columns if any(x in col.lower() for x in
                   ['ethanol', 'ghb', 'carisoprodol'])],
    'hallucinogens': [col for col in drug_columns if any(x in col.lower() for x in
                     ['pcp', 'ketamine', 'hallucinogenic'])],
    'inhalants': [col for col in drug_columns if any(x in col.lower() for x in
                 ['inhalant', 'freon', 'toluene', 'nitrous', 'helium'])],
    'cannabis': [col for col in drug_columns if 'cannabinoid' in col.lower()],
    'other': []
}

# Assign uncategorized drugs to 'other'
categorized_drugs = set()
for category_drugs in DRUG_CATEGORIES.values():
    categorized_drugs.update(category_drugs)
DRUG_CATEGORIES['other'] = [col for col in drug_columns if col not in categorized_drugs]

# Create drug aliases for better matching
DRUG_ALIASES = {
    'fentanyl': ['fent', 'fentanyl'],
    'cocaine': ['coke', 'cocaine'],
    'heroin': ['heroin', 'diacetylmorphine'],
    'methamphetamine': ['meth', 'methamphetamine', 'crystal'],
    'alprazolam': ['xanax', 'alprazolam'],
    'oxycodone': ['oxy', 'oxycodone', 'oxycontin'],
    'hydrocodone': ['vicodin', 'hydrocodone'],
    'ethanol': ['alcohol', 'ethanol', 'booze'],
    'cannabinoids': ['marijuana', 'weed', 'cannabis', 'cannabinoids', 'thc'],
    'morphine': ['morphine'],
    'clonazepam': ['klonopin', 'clonazepam'],
    'diazepam': ['valium', 'diazepam'],
    'lorazepam': ['ativan', 'lorazepam']
}

# Enhanced Sidebar with more features
with st.sidebar:
    st.markdown(f"<h2 style='color:{PRIMARY_COLOR}'>🔎 Query Assistant</h2>", unsafe_allow_html=True)

    # Dataset overview
    with st.expander("📊 Dataset Overview", expanded=False):
        st.write(f"**Total Records:** {len(df):,}")
        st.write(f"**Years Covered:** {df['year'].min():.0f} - {df['year'].max():.0f}")
        st.write(f"**Counties:** {df['county name'].nunique()}")
        st.write(f"**Substances Tracked:** {len(drug_columns)}")

    # Query examples by category
    st.markdown(f"<h3 style='color:{SECONDARY_COLOR}'>💡 Example Queries</h3>", unsafe_allow_html=True)

    query_categories = {
        "📈 Trends & Statistics": [
            "How many fentanyl deaths in 2022?",
            "Number of fentanyl deaths in 2023",
            "Show cocaine trends over time",
            "What's the average age of heroin victims?"
        ],
        "🗺️ Geographic Analysis": [
            "Map of fentanyl deaths in Florida",
            "Which county has most overdoses?",
            "Show Miami-Dade cocaine deaths",
            "Map of heroin deaths in Florida"
        ],
        "🔍 Detailed Analysis": [
            "Demographics of methamphetamine users",
            "Show data table for alprazolam",
            "Demographics of fentanyl users",
            "Show data table for cocaine"
        ],
        "📊 Comparisons": [
            "Demographics of opioid users",
            "What's the average age of cocaine victims?",
            "Which county has most fentanyl deaths?",
            "Demographics of heroin users"
        ]
    }

    for category, examples in query_categories.items():
        with st.expander(category, expanded=False):
            for example in examples:
                if st.button(example, key=f"example_{example}", use_container_width=True):
                    st.session_state.example_query = example

    # Available drugs quick reference
    with st.expander("💊 Available Substances", expanded=False):
        for category, drugs in DRUG_CATEGORIES.items():
            if drugs and category != 'other':
                st.write(f"**{category.title()}:** {', '.join(drugs[:5])}")
                if len(drugs) > 5:
                    st.write(f"   ...and {len(drugs)-5} more")

    # Query tips
    with st.expander("💡 Query Tips", expanded=False):
        st.markdown("""
        **Keywords to use:**
        - **Years:** 2020, 2021, 2022, etc.
        - **Counties:** Miami-Dade, Broward, etc.
        - **Intents:** map, chart, table, compare
        - **Status:** caused by, present in
        - **Demographics:** male, female, age
        """)

    # Data quality info
    with st.expander("ℹ️ Data Notes", expanded=False):
        st.markdown("""
        - **C** = Substance caused death
        - **P** = Substance present but not causal
        - Data from Florida Medical Examiners
        - Updated through 2023
        """)

    st.markdown("---")
    st.markdown(f"<small style='color:{PRIMARY_COLOR}'>Florida Drug Overdose Analytics v2.0</small>", unsafe_allow_html=True)

# Main header with enhanced styling
st.markdown(
    f"""
    <div style='text-align: center; padding: 20px; background: linear-gradient(90deg, {PRIMARY_COLOR}15, {SECONDARY_COLOR}15); border-radius: 10px; margin-bottom: 30px;'>
        <h1 style='color: {PRIMARY_COLOR}; margin-bottom: 10px;'>💊 Florida Drug Overdose Analytics</h1>
        <p style='color: {SECONDARY_COLOR}; font-size: 18px; margin: 0;'>Advanced Query Interface for Medical Examiner Data</p>
    </div>
    """,
    unsafe_allow_html=True,
)

# Enhanced input with session state management
if 'example_query' in st.session_state:
    default_query = st.session_state.example_query
    del st.session_state.example_query
else:
    default_query = ""

user_input = st.text_input(
    "🔍 Ask a question about Florida overdose data:",
    value=default_query,
    placeholder="e.g., How many fentanyl deaths in Miami-Dade in 2022?",
    help="Try asking about trends, maps, comparisons, or specific substances and locations"
)

def find_drug_matches(text: str) -> List[str]:
    """Enhanced drug matching with aliases and fuzzy matching"""
    text_lower = text.lower()
    found_drugs = []

    # Special handling for fentanyl - include both fentanyl and fentanylanalogs
    if any(term in text_lower for term in ['fentanyl', 'fent']):
        fentanyl_cols = [col for col in drug_columns if col in ['fentanyl', 'fentanylanalogs']]
        found_drugs.extend(fentanyl_cols)

    # Direct column name matches (excluding illicitfentanyl)
    for drug in drug_columns:
        if drug != 'illicitfentanyl' and drug.lower() in text_lower:
            found_drugs.append(drug)

    # Alias matching
    for drug, aliases in DRUG_ALIASES.items():
        for alias in aliases:
            if alias.lower() in text_lower:
                if drug == 'fentanyl':
                    # For fentanyl, add both columns
                    fentanyl_cols = [col for col in drug_columns if col in ['fentanyl', 'fentanylanalogs']]
                    found_drugs.extend(fentanyl_cols)
                else:
                    # Find the actual column name
                    matching_cols = [col for col in drug_columns if drug.lower() in col.lower() and col != 'illicitfentanyl']
                    found_drugs.extend(matching_cols)

    # Additional specific matching for common drugs
    drug_mappings = {
        'cocaine': 'cocaine',
        'coke': 'cocaine',
        'heroin': 'heroin',
        'meth': 'methamphetamine',
        'crystal': 'methamphetamine',
        'xanax': 'alprazolam',
        'alcohol': 'ethanol',
        'weed': 'cannabinoids',
        'marijuana': 'cannabinoids'
    }

    for term, drug_name in drug_mappings.items():
        if term in text_lower and drug_name in drug_columns:
            found_drugs.append(drug_name)

    return list(set(found_drugs))  # Remove duplicates

def extract_demographics(text: str) -> Dict[str, Any]:
    """Extract demographic filters from query"""
    demographics = {}
    text_lower = text.lower()

    # Gender
    if any(word in text_lower for word in ['male', 'men', 'man']):
        demographics['sex'] = 'M'
    elif any(word in text_lower for word in ['female', 'women', 'woman']):
        demographics['sex'] = 'F'

    # Age ranges
    age_patterns = [
        (r'under (\d+)', lambda m: ('age_max', int(m.group(1)))),
        (r'over (\d+)', lambda m: ('age_min', int(m.group(1)))),
        (r'(\d+) to (\d+)', lambda m: ('age_range', (int(m.group(1)), int(m.group(2))))),
        (r'teens?', lambda m: ('age_range', (13, 19))),
        (r'elderly|seniors?', lambda m: ('age_min', 65)),
        (r'young adults?', lambda m: ('age_range', (18, 35))),
        (r'middle.?aged?', lambda m: ('age_range', (35, 65)))
    ]

    for pattern, extractor in age_patterns:
        match = re.search(pattern, text_lower)
        if match:
            key, value = extractor(match)
            demographics[key] = value
            break

    # Race/ethnicity
    race_mapping = {
        'white': 'White', 'black': 'Black', 'hispanic': 'Hispanic',
        'asian': 'Asian', 'latino': 'Hispanic', 'african american': 'Black'
    }

    for race_term, race_value in race_mapping.items():
        if race_term in text_lower:
            demographics['race'] = race_value
            break

    return demographics

def parse_query(text: str) -> Dict[str, Any]:
    """Enhanced query parsing with better intent detection and error handling"""
    if not text or not text.strip():
        return {"error": "Empty query"}

    text_lower = text.lower().strip()
    query = {
        "original_text": text,
        "drugs": [],
        "drug_categories": [],
        "years": [],
        "year_range": None,
        "counties": [],
        "demographics": {},
        "status": ["C", "P"],  # Default to both
        "intent": "count",
        "modifiers": [],
        "comparison": False,
        "error": None
    }

    try:
        # Extract years and year ranges
        year_matches = re.findall(r'(19|20)\d{2}', text)
        if year_matches:
            query["years"] = [int(year) for year in year_matches]
            if len(query["years"]) == 2:
                query["year_range"] = tuple(sorted(query["years"]))

        # Extract drugs
        query["drugs"] = find_drug_matches(text_lower)

        # Extract drug categories
        for category, drugs in DRUG_CATEGORIES.items():
            if category in text_lower or any(drug in text_lower for drug in drugs[:3]):
                query["drug_categories"].append(category)

        # Extract counties with better matching
        counties = df["county name"].dropna().unique()
        for county in counties:
            county_variations = [
                county.lower(),
                county.lower().replace('-', ' '),
                county.lower().replace(' ', ''),
                county.lower().split()[0] if ' ' in county else county.lower()
            ]
            if any(var in text_lower for var in county_variations):
                query["counties"].append(county)

        # Extract demographics
        query["demographics"] = extract_demographics(text)

        # Determine status (cause vs present)
        if any(word in text_lower for word in ["caused by", "cause of", "causes", "caused", "fatal", "lethal"]):
            query["status"] = ["C"]
        elif any(word in text_lower for word in ["present", "presence", "detected", "found", "with", "involving"]):
            query["status"] = ["P"]

        # Enhanced intent detection (order matters - more specific patterns first)
        intent_patterns = [
            (r'\b(map)\b.*\b(florida|county|counties|deaths)\b', 'map'),
            (r'\b(map|geographic|spatial|location)\b', 'map'),
            (r'\b(show)\b.*\b(trend|over time)\b', 'plot_over_time'),
            (r'\b(chart|plot|graph|trend|over time|timeline)\b', 'plot_over_time'),
            (r'\b(show)\b.*\b(data table|table)\b', 'table'),
            (r'\b(table|data|list)\b.*\b(for|of)\b', 'table'),
            (r'\b(demographics)\b', 'demographics'),
            (r'\b(which county has most)\b', 'most_deaths_by_county'),
            (r'\b(most)\b.*\b(county|counties)\b', 'most_deaths_by_county'),
            (r'\b(least|lowest|fewest|minimum)\b.*\b(county|counties)\b', 'least_deaths_by_county'),
            (r'\b(most|highest|peak)\b.*\b(year)\b', 'most_deaths_by_year'),
            (r'\b(first|earliest|initial|when did)\b', 'first_appearance'),
            (r'\b(what\'s the average age|average age)\b', 'average_age'),
            (r'\b(average|mean|median)\b.*\b(age)\b', 'average_age'),
            (r'\b(polydrug|multiple|combination)\b', 'polydrug_analysis'),
            (r'\b(suicide|homicide|accident|natural|manner)\b', 'manner_of_death'),
            (r'\b(show)\b.*\b(miami-dade|broward|county)\b', 'count'),
            (r'\b(number of|total|sum|count|how many)\b', 'count')
        ]

        for pattern, intent in intent_patterns:
            if re.search(pattern, text_lower):
                query["intent"] = intent
                break

        # Detect comparison queries
        if any(word in text_lower for word in ['vs', 'versus', 'compare', 'comparison', 'against']):
            query["comparison"] = True

        # Extract modifiers
        modifiers = []
        if 'trend' in text_lower or 'over time' in text_lower:
            modifiers.append('trend')
        if 'percentage' in text_lower or 'percent' in text_lower or '%' in text:
            modifiers.append('percentage')
        if 'rate' in text_lower:
            modifiers.append('rate')
        query["modifiers"] = modifiers

        return query

    except Exception as e:
        logger.error(f"Error parsing query '{text}': {str(e)}")
        query["error"] = f"Error parsing query: {str(e)}"
        return query

def apply_filters(data: pd.DataFrame, query: Dict[str, Any]) -> pd.DataFrame:
    """Apply all filters to the dataset based on query parameters"""
    dff = data.copy()

    try:
        # Year filtering
        if query.get("years"):
            if query.get("year_range"):
                start_year, end_year = query["year_range"]
                dff = dff[(dff['year'] >= start_year) & (dff['year'] <= end_year)]
            else:
                dff = dff[dff['year'].isin(query["years"])]

        # Drug filtering
        if query.get("drugs"):
            drug_conditions = []
            for drug in query["drugs"]:
                if drug in dff.columns:
                    drug_conditions.append(dff[drug].isin(query["status"]))

            if drug_conditions:
                # For fentanyl queries with multiple columns (fentanyl + fentanylanalogs),
                # combine with OR logic so a case matches if it has either
                combined_condition = drug_conditions[0]
                for condition in drug_conditions[1:]:
                    combined_condition = combined_condition | condition
                dff = dff[combined_condition]

        # County filtering
        if query.get("counties"):
            dff = dff[dff["county name"].isin(query["counties"])]

        # Demographics filtering
        demographics = query.get("demographics", {})
        if demographics.get("sex"):
            dff = dff[dff["sex"] == demographics["sex"]]

        if demographics.get("race"):
            dff = dff[dff["race"] == demographics["race"]]

        if demographics.get("age_min"):
            dff = dff[dff["age"] >= demographics["age_min"]]

        if demographics.get("age_max"):
            dff = dff[dff["age"] <= demographics["age_max"]]

        if demographics.get("age_range"):
            min_age, max_age = demographics["age_range"]
            dff = dff[(dff["age"] >= min_age) & (dff["age"] <= max_age)]

        return dff

    except Exception as e:
        logger.error(f"Error applying filters: {str(e)}")
        return data  # Return original data if filtering fails

def create_enhanced_visualizations(data: pd.DataFrame, query: Dict[str, Any]) -> None:
    """Create enhanced visualizations based on query intent"""

    if query["intent"] == "plot_over_time":
        # Enhanced time series plot
        drug_name = query["drugs"][0] if query["drugs"] else "All Substances"

        if query["drugs"]:
            trend_data = []
            for drug in query["drugs"]:
                if drug in data.columns:
                    yearly_counts = data[data[drug].isin(query["status"])].groupby("year").size()
                    for year, count in yearly_counts.items():
                        trend_data.append({"Year": year, "Count": count, "Drug": drug.title()})

            if trend_data:
                trend_df = pd.DataFrame(trend_data)

                fig = px.line(
                    trend_df,
                    x="Year",
                    y="Count",
                    color="Drug" if len(query["drugs"]) > 1 else None,
                    title=f"📈 {drug_name} Deaths Over Time",
                    markers=True
                )
                fig.update_layout(
                    xaxis_title="Year",
                    yaxis_title="Number of Deaths",
                    hovermode='x unified'
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            # All deaths trend
            yearly_counts = data.groupby("year").size().reset_index(name="count")
            fig = px.line(
                yearly_counts,
                x="year",
                y="count",
                title="📈 All Overdose Deaths Over Time",
                markers=True
            )
            st.plotly_chart(fig, use_container_width=True)

    elif query["intent"] == "map":
        if not counties_geojson:
            st.error("🌐 Geographic data unavailable for mapping.")
            return

        map_df = data.groupby("county name").size().reset_index(name="count")
        map_df["county name"] = map_df["county name"].str.title()

        drug_name = query["drugs"][0].title() if query["drugs"] else "All Substances"

        fig = px.choropleth(
            map_df,
            geojson=counties_geojson,
            locations="county name",
            featureidkey="properties.NAME",
            color="count",
            color_continuous_scale="Reds",
            scope="usa",
            title=f"🗺️ {drug_name} Deaths by Florida County",
            labels={"count": "Deaths"}
        )
        fig.update_geos(fitbounds="locations", visible=False)
        fig.update_layout(height=600)
        st.plotly_chart(fig, use_container_width=True)

def handle_query(query: Dict[str, Any]) -> Optional[str]:
    """Enhanced query handling with comprehensive error handling and new features"""

    # Check for parsing errors
    if query.get("error"):
        return f"❌ {query['error']}"

    try:
        # Apply filters
        filtered_data = apply_filters(df, query)

        if filtered_data.empty:
            return "📭 No records found matching your criteria. Try broadening your search."

        # Handle different intents
        if query["intent"] == "count":
            count = len(filtered_data)
            drugs_str = ", ".join([d.title() for d in query["drugs"]]) if query["drugs"] else "all substances"
            years_str = f" in {', '.join(map(str, query['years']))}" if query["years"] else ""
            counties_str = f" in {', '.join(query['counties'])}" if query["counties"] else ""

            # Debug information
            if count == 0 and query["drugs"]:
                debug_info = f"\n\n**Debug Info:**\n"
                debug_info += f"• Searched for drugs: {query['drugs']}\n"
                debug_info += f"• Years: {query['years']}\n"
                debug_info += f"• Status codes: {query['status']}\n"

                # Check if drug columns exist
                missing_drugs = [drug for drug in query["drugs"] if drug not in df.columns]
                if missing_drugs:
                    debug_info += f"• Missing drug columns: {missing_drugs}\n"

                # Check available similar columns
                available_drugs = [col for col in df.columns if col not in NON_DRUG_COLUMNS]
                similar_drugs = [col for col in available_drugs if any(d in col.lower() for d in [drug.lower() for drug in query["drugs"]])]
                if similar_drugs:
                    debug_info += f"• Similar available columns: {similar_drugs}\n"

                return f"📭 No records found matching your criteria.{debug_info}"

            return f"✅ Found **{count:,}** cases involving {drugs_str}{years_str}{counties_str}."

        elif query["intent"] == "most_deaths_by_county":
            result = filtered_data["county name"].value_counts().head(5)
            if result.empty:
                return "No county data available."

            response = "🏆 **Top Counties by Deaths:**\n"
            for i, (county, count) in enumerate(result.items(), 1):
                response += f"{i}. {county}: {count:,} deaths\n"
            return response

        elif query["intent"] == "least_deaths_by_county":
            result = filtered_data["county name"].value_counts().sort_values().head(5)
            if result.empty:
                return "No county data available."

            response = "📉 **Counties with Fewest Deaths:**\n"
            for i, (county, count) in enumerate(result.items(), 1):
                response += f"{i}. {county}: {count:,} deaths\n"
            return response

        elif query["intent"] == "most_deaths_by_year":
            result = filtered_data["year"].value_counts().sort_index(ascending=False).head(5)
            if result.empty:
                return "No yearly data available."

            response = "📅 **Years with Most Deaths:**\n"
            for year, count in result.items():
                response += f"• {year:.0f}: {count:,} deaths\n"
            return response

        elif query["intent"] == "average_age":
            avg_age = filtered_data["age"].mean()
            median_age = filtered_data["age"].median()
            return f"📊 **Age Statistics:** Average: {avg_age:.1f} years, Median: {median_age:.1f} years"

        elif query["intent"] == "demographics":
            st.subheader("👥 Demographic Analysis")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.write("**Gender Distribution**")
                gender_counts = filtered_data["sex"].value_counts()
                fig = px.pie(values=gender_counts.values, names=gender_counts.index, title="Gender")
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                st.write("**Age Distribution**")
                fig = px.histogram(filtered_data, x="age", nbins=20, title="Age Distribution")
                st.plotly_chart(fig, use_container_width=True)

            with col3:
                st.write("**Race Distribution**")
                race_counts = filtered_data["race"].value_counts().head(5)
                fig = px.bar(x=race_counts.index, y=race_counts.values, title="Race/Ethnicity")
                st.plotly_chart(fig, use_container_width=True)

            return None

        elif query["intent"] == "first_appearance":
            if query["drugs"]:
                drug = query["drugs"][0]
                if drug in df.columns:
                    first_year = df[df[drug].isin(query["status"])]["year"].min()
                    return f"🕐 First appearance of {drug.title()} ({' or '.join(query['status'])}) was in {first_year:.0f}."
                else:
                    return f"❌ Drug '{drug}' not found in dataset."
            else:
                return "❌ Drug must be specified for this query."

        elif query["intent"] == "polydrug_analysis":
            polydrug_cases = filtered_data[filtered_data['poly'] == 1]
            total_poly = len(polydrug_cases)
            avg_substances = filtered_data['totalsubstancecount'].mean()

            response = f"💊 **Polydrug Analysis:**\n"
            response += f"• Cases with multiple substances: {total_poly:,}\n"
            response += f"• Percentage of total: {(total_poly/len(filtered_data)*100):.1f}%\n"
            response += f"• Average substances per case: {avg_substances:.1f}"

            return response

        elif query["intent"] == "manner_of_death":
            st.subheader("⚰️ Manner of Death Analysis")

            manner_counts = filtered_data["manner of death"].value_counts()

            # Create pie chart
            fig = px.pie(
                values=manner_counts.values,
                names=manner_counts.index,
                title="Distribution by Manner of Death"
            )
            st.plotly_chart(fig, use_container_width=True)

            # Show statistics
            response = "**Manner of Death Breakdown:**\n"
            for manner, count in manner_counts.items():
                percentage = (count / len(filtered_data)) * 100
                response += f"• {manner}: {count:,} ({percentage:.1f}%)\n"

            return response

        elif query["intent"] in ["plot_over_time", "map"]:
            create_enhanced_visualizations(filtered_data, query)
            return None

        elif query["intent"] == "table":
            st.subheader("📋 Data Table")

            # Select relevant columns
            display_cols = ["date", "age", "sex", "race", "county name", "manner of death"]
            if query["drugs"]:
                display_cols.extend([drug for drug in query["drugs"] if drug in filtered_data.columns])

            display_data = filtered_data[display_cols].head(100)  # Limit to 100 rows
            st.dataframe(display_data, use_container_width=True)

            if len(filtered_data) > 100:
                st.info(f"Showing first 100 of {len(filtered_data):,} total records.")

            return None

        else:
            return "⚠️ I couldn't understand your request. Please try rephrasing your question."

    except Exception as e:
        logger.error(f"Error handling query: {str(e)}")
        return f"❌ An error occurred while processing your query: {str(e)}"

# Additional helper functions
def display_query_interpretation(query: Dict[str, Any]) -> None:
    """Display how the query was interpreted"""
    if st.checkbox("🔍 Show query interpretation", help="See how your query was parsed"):
        with st.expander("Query Analysis", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**Detected Elements:**")
                if query.get("drugs"):
                    st.write(f"• Drugs: {', '.join(query['drugs'])}")
                else:
                    st.write("• Drugs: None detected")

                if query.get("years"):
                    st.write(f"• Years: {', '.join(map(str, query['years']))}")
                else:
                    st.write("• Years: None detected")

                if query.get("counties"):
                    st.write(f"• Counties: {', '.join(query['counties'])}")
                else:
                    st.write("• Counties: None detected")

                if query.get("demographics"):
                    st.write(f"• Demographics: {query['demographics']}")

            with col2:
                st.write("**Query Settings:**")
                st.write(f"• Intent: {query.get('intent', 'unknown')}")
                st.write(f"• Status: {', '.join(query.get('status', []))}")
                st.write(f"• Original text: '{query.get('original_text', '')}'")
                if query.get("comparison"):
                    st.write("• Comparison query detected")
                if query.get("error"):
                    st.error(f"• Error: {query['error']}")

            # Debug info for drug matching
            st.write("**Debug Info:**")
            available_drugs = [col for col in df.columns if col not in NON_DRUG_COLUMNS]
            fentanyl_cols = [col for col in available_drugs if 'fentanyl' in col.lower()]
            target_fentanyl_cols = [col for col in fentanyl_cols if col in ['fentanyl', 'fentanylanalogs']]
            st.write(f"• All fentanyl columns: {fentanyl_cols}")
            st.write(f"• Target fentanyl columns (used for queries): {target_fentanyl_cols}")
            st.write(f"• Total drug columns: {len(available_drugs)}")

            # Show sample data for detected drugs
            if query.get("drugs"):
                st.write("**Sample Data Check:**")
                for drug in query["drugs"][:2]:  # Show first 2 drugs
                    if drug in df.columns:
                        sample_values = df[drug].value_counts().head(3)
                        st.write(f"• {drug} top values: {dict(sample_values)}")

def show_data_insights() -> None:
    """Show quick data insights"""
    with st.expander("📊 Quick Data Insights", expanded=False):
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Cases", f"{len(df):,}")

        with col2:
            latest_year = df['year'].max()
            latest_count = len(df[df['year'] == latest_year])
            st.metric(f"{latest_year:.0f} Cases", f"{latest_count:,}")

        with col3:
            top_drug = df[drug_columns].apply(lambda x: (x == 'C').sum() + (x == 'P').sum()).idxmax()
            top_count = df[drug_columns].apply(lambda x: (x == 'C').sum() + (x == 'P').sum()).max()
            st.metric("Top Substance", f"{top_drug.title()}")
            st.caption(f"{top_count:,} cases")

        with col4:
            top_county = df['county name'].value_counts().index[0]
            top_county_count = df['county name'].value_counts().iloc[0]
            st.metric("Top County", top_county)
            st.caption(f"{top_county_count:,} cases")

# Main execution
if user_input:
    with st.spinner("🔍 Analyzing your query..."):
        query = parse_query(user_input)

        # Display query interpretation
        display_query_interpretation(query)

        # Handle the query
        response = handle_query(query)

        # Display results
        if response:
            if query.get("intent") in ["plot_over_time", "map", "demographics", "table"]:
                # These intents handle their own display
                if response:  # Only show text response if there is one
                    st.success(response)
            else:
                st.success(response)

        # Show additional insights for certain queries
        if query.get("intent") == "count" and len(apply_filters(df, query)) > 0:
            filtered_data = apply_filters(df, query)

            with st.expander("📈 Additional Insights", expanded=False):
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**Top 5 Counties:**")
                    top_counties = filtered_data['county name'].value_counts().head(5)
                    for county, count in top_counties.items():
                        st.write(f"• {county}: {count:,}")

                with col2:
                    st.write("**Demographics:**")
                    avg_age = filtered_data['age'].mean()
                    male_pct = (filtered_data['sex'] == 'M').mean() * 100
                    st.write(f"• Average age: {avg_age:.1f} years")
                    st.write(f"• Male: {male_pct:.1f}%")
                    st.write(f"• Female: {100-male_pct:.1f}%")

else:
    # Show welcome message and data insights when no query is entered
    st.markdown("""
    ### 👋 Welcome to Florida Drug Overdose Analytics

    This advanced chatbot helps you explore Florida's medical examiner data spanning 2003-2023.
    You can ask questions about:

    - **Specific substances** (fentanyl, cocaine, heroin, etc.)
    - **Geographic patterns** (county-level analysis)
    - **Temporal trends** (yearly changes, peaks)
    - **Demographics** (age, gender, race patterns)
    - **Comparisons** (substance vs substance, county vs county)

    **Try asking:** *"How many fentanyl deaths in Miami-Dade in 2022?"*
    """)

    show_data_insights()

    # Show recent trends
    st.subheader("📈 Recent Trends (2020-2023)")
    recent_data = df[df['year'] >= 2020].groupby('year').size().reset_index(name='count')
    fig = px.bar(recent_data, x='year', y='count', title="Total Overdose Deaths by Year")
    st.plotly_chart(fig, use_container_width=True)

# Footer
st.markdown("---")
st.markdown(
    f"""
    <div style='text-align: center; color: {PRIMARY_COLOR}; padding: 20px;'>
        <small>
        Florida Drug Overdose Analytics v2.0 | Data: Florida Medical Examiners Commission<br>
        For research and educational purposes only
        </small>
    </div>
    """,
    unsafe_allow_html=True
)
